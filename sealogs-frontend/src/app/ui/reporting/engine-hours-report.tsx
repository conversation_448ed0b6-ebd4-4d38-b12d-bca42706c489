'use client'

import Filter from '@/components/filter'
import { GET_ENGINE_HOURS_REPORT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { useMemo, useState } from 'react'
import ExportButton from './export-button'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import {
    Table,
    TableBody,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

import { ArrowLeft } from 'lucide-react'
import { Button, Card, CardContent, H1, ListHeader } from '@/components/ui'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

interface IReportItem {
    vesselName: string
    logbookDate: Date
    engineName: string
    hoursStart: number
    hoursEnd: number
    runHours: number
}

const tableHeadings = [
    'Vessel',
    'Log Entry',
    'Engine Name',
    'Engine Hours Start',
    'Engine Hours End',
    'Engine Run Hours',
]

export default function EngineHoursReport() {
    const router = useRouter()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_ENGINE_HOURS_REPORT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('GET_ENGINE_HOURS_REPORT_ENTRIES error', error)
            },
        },
    )

    const handleFilterOnChange = ({
        type,
        data,
    }: {
        type: 'dateRange' | 'vessels'
        data: any
    }) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'vessels':
                setSelectedVessels(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (selectedVessels.length > 0) {
            filter['vehicleID'] = {
                in: selectedVessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData: any = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach((item: any) => {
            if (item.state !== 'Locked') {
                return
            }

            if (item.vehicle.id == 0) {
                return
            }

            const engineStartStopEntries = item.engineStartStop.nodes
            if (engineStartStopEntries.length === 0) {
                return
            }

            engineStartStopEntries.forEach((engineStartStop: any) => {
                if (engineStartStop.engine.id == 0) {
                    return
                }

                const reportItem: IReportItem = {
                    vesselName: item.vehicle.title,
                    logbookDate: new Date(item.startDate),
                    engineName: engineStartStop.engine.title,
                    hoursStart: engineStartStop.hoursStart ?? 0,
                    hoursEnd: engineStartStop.hoursEnd ?? 0,
                    runHours: engineStartStop.totalHours ?? 0,
                }

                reportItems.push(reportItem)
            })
        })

        return reportItems
    }, [called, loading, data])

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            [
                'Vessel',
                'Log Entry',
                'Engine Name',
                'Engine Hours Start',
                'Engine Hours End',
                'Engine Run Hours',
            ],
        ]

        const data: any = reportData.map(function (item) {
            return [
                item.vesselName + '',
                dayjs(item.logbookDate).format('DD/MM/YY') + '',
                item.engineName + '',
                item.hoursStart.toLocaleString(),
                item.hoursEnd.toLocaleString(),
                item.runHours.toLocaleString(),
            ]
        })

        const totalRunHours = reportData.reduce<number>(
            (acc, current) => acc + current.runHours,
            0,
        )

        exportPdfTable({
            headers,
            body: data,
            footers: [
                [
                    {
                        colSpan: 5,
                        content: 'Total Run Hours',
                    },
                    {
                        content: totalRunHours.toLocaleString(),
                    },
                ],
            ],
            userOptions: {
                showFoot: 'lastPage',
            },
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return []
        }

        const csvEntries: string[][] = [
            [
                'vessel',
                'log entry',
                'engine name',
                'engine hours start',
                'engine hours end',
                'engine run hours',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                dayjs(item.logbookDate).format('DD/MM/YYYY'),
                item.engineName,
                item.hoursStart.toLocaleString(),
                item.hoursEnd.toLocaleString(),
                item.runHours.toLocaleString(),
            ])
        })

        exportCsv(csvEntries)
    }

    return (
        <>
            <ListHeader
                title="Engine Hours Report"
                actions={
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter
                        onChange={handleFilterOnChange}
                        onClick={generateReport}
                    />
                    <ExportButton
                        onDownloadCsv={downloadCsv}
                        onDownloadPdf={downloadPdf}
                    />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {tableHeadings.map((heading) => (
                                    <TableHead key={heading}>
                                        {heading}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableContent
                            isLoading={called && loading}
                            reportData={reportData}
                        />
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    const totalRunHours = useMemo<number>(() => {
        return reportData.reduce<number>(
            (acc, current) => current.runHours + acc,
            0,
        )
    }, [reportData])

    if (isLoading) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center  h-32">
                        Loading...
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center  h-32">
                        No Data Available
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    return (
        <>
            <TableBody>
                {reportData.map((element: IReportItem, index: number) => {
                    return (
                        <TableRow
                            key={`report-entry-${index}`}
                            className={`group border-b  hover: `}>
                            <TableCell className="px-2 py-3 text-left">
                                <div className=" inline-block ml-3">
                                    {element.vesselName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left">
                                <div className=" inline-block ">
                                    {dayjs(element.logbookDate).format(
                                        'DD/M/YY',
                                    )}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left">
                                <div className=" inline-block ">
                                    {element.engineName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left">
                                <div className=" inline-block ">
                                    {element.hoursStart.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left">
                                <div className=" inline-block ">
                                    {element.hoursEnd.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left">
                                <div className=" inline-block ">
                                    {element.runHours.toLocaleString()}
                                </div>
                            </TableCell>
                        </TableRow>
                    )
                })}
            </TableBody>
            <TableFooter>
                <TableRow className={`group border-b  `}>
                    <TableCell
                        className="px-2 py-3 text-left"
                        scope="col"
                        colSpan={5}>
                        <div className="inline-block ml-3">Total Run Hours</div>
                    </TableCell>
                    <TableCell className="px-2 py-3 text-left">
                        <div className="inline-block ">
                            {totalRunHours.toLocaleString()}
                        </div>
                    </TableCell>
                </TableRow>
            </TableFooter>
        </>
    )
}
