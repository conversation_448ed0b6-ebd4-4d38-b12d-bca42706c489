'use client'

import { But<PERSON> } from '@/components/ui/button'
import { getFieldName } from '@/app/lib/actions'
import React, { useCallback, useEffect, useState } from 'react'
import VesselRescue from './forms/vessel-rescue'
import PersonRescue from './forms/person-rescue'
import RestrictedVisibility from './forms/restricted-visibility'
import BarCrossing from './forms/bar-crossing'
import PassengerDropFacility from './forms/passenger-drop-facility'
import Tasking from './forms/tasking'
import { useSearchParams } from 'next/navigation'
import CrewTrainingEvent from './forms/crew-training-event'
import SupernumeraryEvent from './forms/supernumerary-event'
import PassengerVehiclePickDrop from './forms/passenger-vehicle-pick-drop'
import { uniqueId } from 'lodash'
import RefuellingBunkering from './forms/refuelling-bunkering'
import vesselTypes from '@/app/lib/vesselTypes'
import { SLALL_LogBookFields } from '@/app/lib/logbook-configuration'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import InfringementNotices from './forms/InfringementNotices'
import TripUpdate from './forms/trip-update'
import dayjs from 'dayjs'
import { Combobox } from '@/components/ui/comboBox'
import { Label } from '@/components/ui/label'
import {
    Accordion,
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from '@/components/ui/accordion'
import { H4, P } from '@/components/ui/typography'
import RadioLogsScheduleService from './radio-logs-schedule'
import IncidentRecordForm from '../incident-record/incident-record-form'
import PilotTransfer from './forms/pilot-transfer'
import { cn } from '@/app/lib/utils'

export default function Events({
    currentTrip,
    logBookConfig,
    updateTripReport,
    locked,
    geoLocations,
    tripReport,
    crewMembers,
    masterID,
    vessel,
    vessels,
    offline = false,
    setSelectedRow,
    setCurrentEventType,
    setCurrentStop,
    currentEventType,
    currentStop,
    tripReport_Stops,
    setTripReport_Stops,
    displayDangerousGoodsPvpd = false,
    displayDangerousGoodsPvpdSailing,
    setDisplayDangerousGoodsPvpd,
    setDisplayDangerousGoodsPvpdSailing,
    allPVPDDangerousGoods,
    setAllPVPDDangerousGoods,
    selectedDGRPVPD,
    setSelectedDGRPVPD,
    fuelLogs,
    logBookStartDate,
}: {
    currentTrip: any
    logBookConfig: any
    updateTripReport: any
    locked: boolean
    geoLocations: any
    tripReport: any
    crewMembers: any
    masterID: any
    vessel: any
    vessels: any
    offline?: boolean
    setSelectedRow: any
    setCurrentEventType: any
    setCurrentStop: any
    currentEventType: any
    currentStop: any
    tripReport_Stops: any
    setTripReport_Stops: any
    displayDangerousGoodsPvpd: boolean
    displayDangerousGoodsPvpdSailing: any
    setDisplayDangerousGoodsPvpd: any
    setDisplayDangerousGoodsPvpdSailing: any
    allPVPDDangerousGoods: any
    setAllPVPDDangerousGoods: any
    selectedDGRPVPD: any
    setSelectedDGRPVPD: any
    fuelLogs?: any
    logBookStartDate?: any
}) {
    const [events, setEvents] = useState<any>(false)
    const [openEventModal, setOpenEventModal] = useState(false)
    const [currentEvent, setCurrentEvent] = useState<any>(false)
    const [taskingEvents, setTaskingEvents] = useState<any>(0)
    const [accordionValue, setAccordionValue] = useState<string>('')
    const vesselID = useSearchParams().get('vesselID') || '0'

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_tripActivity, setEdit_tripActivity] = useState<any>(false)
    const [displayRadioLogs, setDisplayRadioLogs] = useState(false)
    const [activityTypeOptions, setActivityTypeOptions] = useState<any>([])
    const init_permissions = () => {
        if (permissions) {
            if (
                hasPermission(
                    process.env.EDIT_LOGBOOKENTRY_ACTIVITY ||
                        'EDIT_LOGBOOKENTRY_ACTIVITY',
                    permissions,
                )
            ) {
                setEdit_tripActivity(true)
            } else {
                setEdit_tripActivity(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const initData = () => {
        const combinedFields = logBookConfig?.customisedLogBookComponents?.nodes
            .filter(
                (section: any) =>
                    section.componentClass ===
                        'SeaLogs\\EventType_LogBookComponent' ||
                    section.componentClass === 'EventType_LogBookComponent',
            )
            .reduce((acc: any, section: any) => {
                acc = acc.concat(section.customisedComponentFields.nodes)
                return acc
            }, [])
        const hasRescueType = combinedFields?.find(
            (field: any) =>
                field.fieldName === 'VesselRescue' ||
                field.fieldName === 'HumanRescue',
        )
        if (logBookConfig) {
            const eventList = hasRescueType
                ? combinedFields.filter(
                      (field: any) =>
                          !hasParent(field) && field.status !== 'Off',
                  )
                : combinedFields?.filter(
                      (field: any) =>
                          !hasParent(field) &&
                          field.status !== 'Off' &&
                          field.fieldName !== 'TaskingStartUnderway' &&
                          field.fieldName !== 'TaskingOnScene' &&
                          field.fieldName !== 'TaskingOnTow' &&
                          field.fieldName !== 'TaskingPaused' &&
                          field.fieldName !== 'TaskingResumed' &&
                          field.fieldName !== 'TaskingComplete' &&
                          field.fieldName !== 'DangerousGoodsSailing',
                  )
            const filteredEvents = eventList
                ?.map((event: any) => ({
                    label: getFieldName(event)
                        .replace(/([a-z])([A-Z])/g, '$1 $2')
                        .replace('Passenger Arrival', 'Arrival')
                        .replace('Passenger Departure', 'Departure'),
                    value: event.fieldName,
                }))
                .filter(
                    (event: any, index: number, self: any) =>
                        index ===
                        self.findIndex((e: any) => e.value === event.value),
                )
                .filter(
                    (event: any) =>
                        // event?.value !== 'VesselRescue' &&
                        // event?.value !== 'HumanRescue' &&
                        // event?.value !== 'Supernumerary' &&
                        !isTowingField(event.value),
                )
                .filter((event: any) => checkVesselType(event.value))

            // Add Incident Record as a custom activity type
            // Incident Record is available for all vessel types
            filteredEvents.push({
                label: 'Incident Record',
                value: 'IncidentRecord',
            })

            // Add Infringement Notices as a custom activity type if vessel type allows it
            // InfringementNotices is only available for vessel types 0 and 1
            const vesselTypeID = vesselTypes.findIndex(
                (type: any) => type == vessel?.vesselType,
            )
            if ([0, 1].includes(vesselTypeID)) {
                filteredEvents.push({
                    label: 'Infringement Notices',
                    value: 'InfringementNotice',
                })
            }

            if (permissions && hasPermission('RECORD_TRAINING', permissions)) {
                setEvents(sortFilteredEvents(filteredEvents))
            } else {
                setEvents(
                    sortFilteredEvents(
                        filteredEvents?.filter(
                            (event: any) => event.value !== 'CrewTraining',
                        ) ?? [],
                    ),
                )
            }
        }
    }
    useEffect(() => {
        initData()
    }, [logBookConfig])

    const checkVesselType = (field: any) => {
        const vesselTypeID = vesselTypes.findIndex(
            (type: any) => type == vessel?.vesselType,
        )
        const config = SLALL_LogBookFields.find(
            (localField: any) =>
                localField.componentClass === 'EventType_LogBookComponent',
        )

        const isVesselType = config?.items.find(
            (localField: any) =>
                field === localField.value &&
                localField.vesselType.includes(vesselTypeID),
        )
        return isVesselType ? true : false
    }

    const sortFilteredEvents = (events: any) => {
        if (
            currentTrip?.tripEvents?.nodes?.find(
                (event: any) =>
                    event.eventCategory === 'Tasking' &&
                    event.eventType_Tasking?.type === 'TaskingStartUnderway' &&
                    event.eventType_Tasking?.status === 'Open',
            )
        ) {
            const openTask = currentTrip?.tripEvents.nodes.filter(
                (event: any) =>
                    event.eventCategory === 'Tasking' &&
                    event.eventType_Tasking?.type === 'TaskingStartUnderway',
            )?.length
            const pausedTask = currentTrip?.tripEvents.nodes.filter(
                (event: any) =>
                    event.eventCategory === 'Tasking' &&
                    event.eventType_Tasking?.type === 'TaskingPaused',
            )?.length
            const sortedEvents = [
                ...events
                    .filter(
                        (event: any) =>
                            event.value === 'TaskingStartUnderway' &&
                            openTask - pausedTask < 1,
                    )
                    .map((event: any) => ({
                        ...event,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    })),
                ...events
                    .filter((event: any) => event.value === 'TaskingOnScene')
                    .map((event: any) => ({
                        ...event,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    })),
                ...events
                    .filter((event: any) => event.value === 'TaskingOnTow')
                    .map((event: any) => ({
                        ...event,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    })),
                ...events
                    .filter((event: any) => event.value === 'TaskingComplete')
                    .map((event: any) => ({
                        ...event,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    })),
                ...events
                    .filter((event: any) => event.value === 'TaskingPaused')
                    .map((event: any) => ({
                        ...event,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    })),
                ...events
                    .filter(
                        (event: any) =>
                            event.value === 'TaskingResumed' && pausedTask > 0,
                    )
                    .map((event: any) => ({
                        ...event,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    })),
                ...events.filter(
                    (event: any) => !event.value.includes('Tasking'),
                ),
            ]
            return sortedEvents
        }
        return events
    }

    /*const colourStyles: StylesConfig = {
        option: (
            styles: any,
            {
                data,
                isDisabled,
                isFocused,
                isSelected,
            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },
        ) => {
            const color = data.color
            return {
                ...styles,
                backgroundColor: isDisabled
                    ? undefined
                    : isSelected
                      ? data.bgColor
                      : isFocused
                        ? data.bgColor
                        : data.bgColor + '60',
                color: data.color,
            }
        },
        singleValue: (styles: any, data: any) => ({
            ...styles,
            color: events.find((option: any) => option.value == data.data.value)
                ?.color,
        }),
    }*/

    const formatTime = (time: string) => time.slice(0, 5)

    useEffect(() => {
        const taskingEvents = currentTrip?.tripEvents?.nodes.filter(
            (event: any) =>
                event?.eventCategory === 'Tasking' &&
                (event?.eventType_Tasking?.type === 'TaskingStartUnderway' ||
                    event?.eventType_Tasking?.type === 'TaskingPaused') &&
                event?.eventType_Tasking?.status === 'Open',
        )?.length
        setTaskingEvents(taskingEvents)
    }, [currentTrip])

    const hasParent = (field: any) => {
        const config = SLALL_LogBookFields.find(
            (localField: any) =>
                localField.componentClass === 'EventType_LogBookComponent',
        )

        const hasGroup = config?.items.find(
            (localField: any) =>
                field.fieldName === localField.value && localField.groupTo,
        )
        return hasGroup ? true : false
    }

    const isTowingField = (field: any) => {
        const config = SLALL_LogBookFields.find(
            (localField: any) =>
                localField.componentClass === 'EventType_LogBookComponent',
        )

        const isTowingCategory = config?.items.find(
            (localField: any) =>
                field === localField.value &&
                localField.type === 'TowingSubCategory',
        )
        return isTowingCategory ? true : false
    }

    const handleEventChange = (event: any) => {
        setCurrentEvent(false)
        setCurrentStop(false)
        setTripReport_Stops(false)
        setDisplayDangerousGoodsPvpd(false)
        setDisplayDangerousGoodsPvpdSailing(null)
        fetchActivityTypes()
        setCurrentEventType(event)
    }

    // const handleSetOpenEventModal = () => {
    // setOpenEventModal(!openEventModal)
    // }

    const handleSetCurrentEventType = () => {
        setCurrentEventType(false)
        // Reset accordion state to properly close it
        setAccordionValue('')
        setSelectedRow(0)
        setCurrentEvent(false)
        setCurrentStop(false)
        // setOpenEventModal(false)
    }

    const previousDropEvent = (currentEvent: any) => {
        const previousEvent = currentTrip?.tripEvents?.nodes.find(
            (event: any) =>
                event?.eventCategory === 'PassengerDropFacility' &&
                event?.id !== currentEvent?.id,
        )
        return previousEvent
    }

    const mainTaskingEvent = (currentEvent: any) => {
        const mainEvent = currentTrip?.tripEvents?.nodes.filter(
            (event: any) =>
                event?.eventCategory === 'Tasking' &&
                event?.id !== currentEvent?.id &&
                event?.eventType_Tasking?.type === 'TaskingStartUnderway',
        )
        return mainEvent
    }

    useEffect(() => {
        if (events) {
            let options: any = []
            if (taskingEvents === 0) {
                options = events.filter(
                    (event: any) =>
                        event?.value !== 'TaskingOnScene' &&
                        event?.value !== 'TaskingOnTow' &&
                        event?.value !== 'TaskingPaused' &&
                        event?.value !== 'TaskingResumed' &&
                        event?.value !== 'TaskingComplete',
                )
            } else {
                const taskingOpen = currentTrip?.tripEvents?.nodes.filter(
                    (event: any) =>
                        event?.eventCategory === 'Tasking' &&
                        event?.eventType_Tasking?.type ===
                            'TaskingStartUnderway' &&
                        event?.eventType_Tasking?.status === 'Open',
                )
                const taskingPaused = currentTrip?.tripEvents?.nodes.filter(
                    (event: any) =>
                        event?.eventCategory === 'Tasking' &&
                        event?.eventType_Tasking?.type === 'TaskingPaused',
                )
                const taskingResumed = currentTrip?.tripEvents?.nodes.filter(
                    (event: any) =>
                        event?.eventCategory === 'Tasking' &&
                        event?.eventType_Tasking?.type === 'TaskingResumed',
                )

                if (taskingOpen?.length > 0) {
                    if (taskingPaused?.length === taskingResumed?.length) {
                        options = events.filter(
                            (event: any) => event?.value !== 'TaskingResumed',
                        )
                    }
                    if (taskingPaused?.length > taskingResumed?.length) {
                        options = events.filter(
                            (event: any) =>
                                event.value !== 'TaskingOnScene' &&
                                event.value !== 'TaskingOnTow' &&
                                event.value !== 'TaskingPaused',
                        )
                    }
                } else {
                    options = events.filter(
                        (event: any) =>
                            event.value !== 'TaskingOnScene' &&
                            event.value !== 'TaskingOnTow' &&
                            event.value !== 'TaskingPaused' &&
                            event.value !== 'TaskingResumed' &&
                            event.value !== 'TaskingComplete',
                    )
                }
            }

            // When taskingPaused > 0, ensure TaskingResumed and TaskingStartUnderway are available
            const taskingPausedCount =
                currentTrip?.tripEvents?.nodes?.filter(
                    (event: any) =>
                        event?.eventCategory === 'Tasking' &&
                        event?.eventType_Tasking?.type === 'TaskingPaused',
                )?.length || 0

            if (taskingPausedCount > 0) {
                // Find TaskingResumed and TaskingStartUnderway from the original events array
                const taskingResumedOption = events.find(
                    (event: any) => event?.value === 'TaskingResumed',
                )
                const taskingStartUnderwayOption = events.find(
                    (event: any) => event?.value === 'TaskingStartUnderway',
                )

                // Add TaskingResumed if it exists in events but not in current options
                if (
                    taskingResumedOption &&
                    !options.find(
                        (option: any) => option.value === 'TaskingResumed',
                    )
                ) {
                    options.push(taskingResumedOption)
                }

                // Add TaskingStartUnderway if it exists in events but not in current options
                if (
                    taskingStartUnderwayOption &&
                    !options.find(
                        (option: any) =>
                            option.value === 'TaskingStartUnderway',
                    )
                ) {
                    options.push(taskingStartUnderwayOption)
                }
            }

            options = options.map((option: any) => {
                if (
                    option.value.includes('Tasking') &&
                    option.value !== 'TaskingStartUnderway' &&
                    !option.className
                ) {
                    return {
                        ...option,
                        className:
                            'bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200',
                    }
                }
                return option
            })
            // Remove duplicate by checking the options.value
            options = options.filter(
                (option: any, index: number, self: any) =>
                    index ===
                    self.findIndex((o: any) => o.value === option.value),
            )
            // Remove InfringementNotices from options because there's already InfringementNotice (without an 's').
            options = options.filter(
                (option: any) => option.value !== 'InfringementNotices',
            )
            setActivityTypeOptions(options)
        }
    }, [events, currentTrip])

    const fetchActivityTypes = () => {
        initData()
    }

    // Memoized function to handle stop accordion item clicks
    const handleStopAccordionItemClick = useCallback(
        (eventId: number) => {
            return () => {
                // Toggle accordion state
                if (accordionValue === `stop_${eventId}`) {
                    setAccordionValue('')
                    setSelectedRow(0)
                    setCurrentEventType([])
                    setCurrentEvent(false)
                    setCurrentStop(false)
                } else {
                    setAccordionValue(`stop_${eventId}`)
                    setSelectedRow(eventId)
                    setCurrentEventType({
                        label: 'Passenger/vehicle pickup/drop off',
                        value: 'PassengerVehiclePickDrop',
                    })
                    // Find the event by ID
                    const event = currentTrip?.tripReport_Stops?.nodes.find(
                        (stop: any) => stop.id === eventId,
                    )
                    setCurrentStop(event)
                    setDisplayDangerousGoodsPvpd(false)
                    setDisplayDangerousGoodsPvpdSailing(null)
                    setTripReport_Stops(false)
                }
            }
        },
        [
            accordionValue,
            currentTrip?.tripReport_Stops?.nodes,
            setSelectedRow,
            setCurrentEventType,
            setCurrentEvent,
            setCurrentStop,
            setDisplayDangerousGoodsPvpd,
            setDisplayDangerousGoodsPvpdSailing,
            setTripReport_Stops,
        ],
    )

    // Memoized function to generate stop display text
    const getStopDisplayText = useCallback((event: any) => {
        return `Passenger / Vehicle Pick & Drop - ${event?.arriveTime ? event?.arriveTime + ' (arr)' : ''} ${event?.arriveTime && event?.departTime ? '-' : ''} ${event?.departTime ? event?.departTime + ' (dep)' : ''} ${event?.stopLocation?.title ? event?.stopLocation?.title : ''}`
    }, [])

    const shouldIndent = (event: any) => {
        return (
            event.eventCategory === 'Tasking' &&
            event.eventType_Tasking?.type !== 'TaskingStartUnderway'
        )
    }

    const getEventLabel = (event: any) => {
        return event?.eventCategory === 'PassengerDropFacility'
            ? event?.eventType_PassengerDropFacility?.type
                  ?.replace(/([a-z])([A-Z])/g, '$1 $2')
                  ?.replace('Passenger Arrival', 'Arrival')
                  ?.replace('Passenger Departure', 'Departure')
            : event?.eventCategory === 'Tasking'
              ? event?.eventType_Tasking?.type.replace(
                    /([a-z])([A-Z])/g,
                    '$1 $2',
                )
              : event?.eventCategory === 'EventSupernumerary'
                ? 'Supernumerary'
                : event?.eventCategory.replace(/([a-z])([A-Z])/g, '$1 $2')
    }

    const getEventValue = (event: any) => {
        return event?.eventCategory === 'PassengerDropFacility'
            ? event?.eventType_PassengerDropFacility?.type
            : event?.eventCategory === 'Tasking'
              ? event?.eventType_Tasking?.type
              : event?.eventCategory === 'Supernumerary'
                ? 'EventSupernumerary'
                : event?.eventCategory
    }

    const getFuelTotals = (fuelLogs: any) => {
        const totalFuel = fuelLogs.reduce((acc: any, log: any) => {
            return acc + log?.fuelAdded
        }, 0)
        const totalCost = fuelLogs.reduce((acc: any, log: any) => {
            return acc + log?.fuelAdded * log?.costPerLitre
        }, 0)
        return (
            ' - Total Fuel Added: ' + totalFuel + 'L, Total Cost: $' + totalCost
        )
    }

    const getEventDisplayText = (event: any) => {
        const category = event.eventCategory
        const eventType = event[`eventType_${category}`]
        const geoLocation = eventType?.geoLocation?.title
        const title = eventType?.title

        switch (category) {
            case 'PassengerDropFacility':
                return (
                    (eventType?.time ? eventType.time + ' - ' : '') +
                    eventType?.type
                        ?.replace(/([a-z])([A-Z])/g, '$1 $2')
                        ?.replace('Passenger Arrival', 'Arrival')
                        ?.replace('Passenger Departure', 'Departure') +
                    (title ? ' - ' + title : '') +
                    (geoLocation ? ' - ' + geoLocation : '')
                )
            case 'Tasking':
                return (
                    eventType?.time +
                    ' - ' +
                    eventType?.type?.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (title ? ' - ' + title : '') +
                    (geoLocation ? ' - ' + geoLocation : '')
                )
            case 'BarCrossing':
                return (
                    (eventType?.time ? eventType.time + ' - ' : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (geoLocation ? ' - ' + geoLocation : '')
                )
            case 'RefuellingBunkering':
                return (
                    (eventType?.date
                        ? dayjs(eventType.date).format('HH:mm') + ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (geoLocation ? ' - ' + geoLocation : '')
                )
            case 'RestrictedVisibility':
                return (
                    (eventType?.crossingTime
                        ? eventType.crossingTime + ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (eventType?.startLocation?.title
                        ? ' - ' + eventType.startLocation.title
                        : '')
                )
            case 'TripUpdate':
                return (
                    (event.tripUpdate?.date
                        ? dayjs(event.tripUpdate.date).format('HH:mm') + ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (event.tripUpdate?.geoLocation?.title
                        ? ' - ' + event.tripUpdate?.geoLocation?.title
                        : '')
                )
            case 'EventSupernumerary':
                return (
                    (event?.supernumerary?.briefingTime
                        ? event?.supernumerary?.briefingTime + ' - '
                        : '') +
                    'Supernumerary' +
                    (event?.supernumerary?.title
                        ? ' - ' + event?.supernumerary.title
                        : '')
                )
            case 'IncidentRecord':
                return (
                    (event.incidentRecord?.startDate
                        ? dayjs(event.incidentRecord.startDate).format(
                              'HH:mm',
                          ) + ' - '
                        : '') +
                    'Incident Record' +
                    (event.incidentRecord?.title
                        ? ' - ' + event.incidentRecord.title
                        : '')
                )
            case 'InfringementNotice':
                return (
                    (event?.infringementNotice?.time
                        ? dayjs(
                              `${dayjs().format('YYYY-MM-DD')} ${event?.infringementNotice.time}`,
                          ).format('HH:mm') + ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (event?.infringementNotice?.geoLocation.title
                        ? ' - ' + event?.infringementNotice?.geoLocation.title
                        : '')
                )
            case 'CrewTraining':
                return (
                    (event.crewTraining?.startTime
                        ? event.crewTraining.startTime + ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (event.crewTraining?.geoLocation?.title
                        ? ' - ' + event.crewTraining?.geoLocation?.title
                        : '')
                )
            case 'VesselRescue':
                return (
                    (event.eventType_VesselRescue?.mission?.completedAt
                        ? event.eventType_VesselRescue?.mission?.completedAt +
                          ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (event.eventType_VesselRescue?.vesselName
                        ? ' - ' + event.eventType_VesselRescue.vesselName
                        : '')
                )
            case 'HumanRescue':
                return (
                    (event.eventType_PersonRescue?.mission?.completedAt
                        ? event.eventType_PersonRescue?.mission?.completedAt +
                          ' - '
                        : '') +
                    category.replace(/([a-z])([A-Z])/g, '$1 $2') +
                    (event.eventType_PersonRescue?.personName
                        ? ' - ' + event.eventType_PersonRescue.personName
                        : '')
                )
            default:
                return category.replace(/([a-z])([A-Z])/g, '$1 $2')
        }
    }

    return (
        <div className="space-y-8" key={uniqueId()}>
            <div className="flex flex-col sm:flex-row justify-between items-end">
                <div className="space-y-2">
                    <H4>ACTIVITIES</H4>
                    <P>
                        Record the events that happen during a voyage in this
                        section.
                    </P>
                </div>
            </div>

            {currentTrip?.tripEvents?.nodes?.length > 0 ||
            currentTrip?.tripReport_Stops?.nodes?.length > 0 ||
            !currentEvent ? (
                <>
                    {(currentTrip?.tripEvents?.nodes?.length > 0 ||
                        currentTrip?.tripReport_Stops?.nodes?.length > 0) && (
                        <Accordion
                            type="single"
                            collapsible
                            value={accordionValue}
                            onValueChange={(value) => {
                                setAccordionValue(value)

                                // If we're closing the accordion, reset the state
                                if (value === '') {
                                    setSelectedRow(0)
                                    // setOpenEventModal(false)
                                    setCurrentEventType([])
                                    setCurrentEvent(false)
                                    setCurrentStop(false)
                                }
                            }}>
                            {currentTrip?.tripEvents.nodes.map(
                                (event: any, index: number) => {
                                    // Generate event label and value outside the JSX
                                    const eventLabel =
                                        event?.eventCategory ===
                                        'PassengerDropFacility'
                                            ? event?.eventType_PassengerDropFacility?.type
                                                  ?.replace(
                                                      /([a-z])([A-Z])/g,
                                                      '$1 $2',
                                                  )
                                                  ?.replace(
                                                      'Passenger Arrival',
                                                      'Arrival',
                                                  )
                                                  ?.replace(
                                                      'Passenger Departure',
                                                      'Departure',
                                                  )
                                            : event?.eventCategory === 'Tasking'
                                              ? event?.eventType_Tasking?.type.replace(
                                                    /([a-z])([A-Z])/g,
                                                    '$1 $2',
                                                )
                                              : event?.eventCategory ===
                                                  'EventSupernumerary'
                                                ? 'Supernumerary'
                                                : event?.eventCategory.replace(
                                                      /([a-z])([A-Z])/g,
                                                      '$1 $2',
                                                  )

                                    const eventValue =
                                        event?.eventCategory ===
                                        'PassengerDropFacility'
                                            ? event
                                                  ?.eventType_PassengerDropFacility
                                                  ?.type
                                            : event?.eventCategory === 'Tasking'
                                              ? event?.eventType_Tasking?.type
                                              : event?.eventCategory ===
                                                  'Supernumerary'
                                                ? 'EventSupernumerary'
                                                : event?.eventCategory

                                    // Generate event display text
                                    const eventDisplayText =
                                        getEventDisplayText(event)

                                    // Handle click on accordion item
                                    const handleAccordionItemClick = () => {
                                        // Toggle accordion state
                                        if (
                                            accordionValue ===
                                            event.id.toString()
                                        ) {
                                            setAccordionValue('')
                                            setSelectedRow(0)
                                            // setOpenEventModal(false)
                                            setCurrentEventType([])
                                            setCurrentEvent(false)
                                            setCurrentStop(false)
                                        } else {
                                            setAccordionValue(
                                                event.id.toString(),
                                            )
                                            setSelectedRow(event.id)
                                            // setOpenEventModal(true)
                                            setCurrentEventType({
                                                label: eventLabel,
                                                value: eventValue,
                                            })
                                            setCurrentEvent(event)
                                            setTripReport_Stops(false)
                                            setDisplayDangerousGoodsPvpd(false)
                                            setDisplayDangerousGoodsPvpdSailing(
                                                null,
                                            )
                                        }
                                    }

                                    return (
                                        <AccordionItem
                                            key={index + '_events'}
                                            value={event.id.toString()}
                                            className={
                                                event?.eventCategory ===
                                                    'Tasking' &&
                                                event?.eventType_Tasking
                                                    ?.type !==
                                                    'TaskingStartUnderway'
                                                    ? 'ml-[1.5rem]'
                                                    : ''
                                            }>
                                            <AccordionTrigger
                                                onClick={
                                                    handleAccordionItemClick
                                                }>
                                                <div className="flex items-center relative justify-between w-full">
                                                    <div className="flex flex-col inset-y-0 items-center justify-center absolute -left-[41px] sm:-left-[46px] w-5">
                                                        {(event?.eventCategory !==
                                                            'Tasking' ||
                                                            event
                                                                ?.eventType_Tasking
                                                                ?.type ==
                                                                'TaskingStartUnderway') && (
                                                            <div
                                                                className={cn(
                                                                    'size-[11px] z-10 rounded-full',
                                                                    currentEvent.id ===
                                                                        event.id
                                                                        ? 'border border-primary bg-curious-blue-200'
                                                                        : currentEvent.eventCategory ===
                                                                            event.eventCategory
                                                                          ? 'border border-primary bg-curious-blue-200'
                                                                          : 'border border-cool-wedgewood-200 bg-outer-space-50',
                                                                )}
                                                            />
                                                        )}
                                                    </div>
                                                    <div className="text-left">
                                                        {eventDisplayText}
                                                        {event?.eventCategory ===
                                                            'RefuellingBunkering' &&
                                                            event
                                                                ?.eventType_RefuellingBunkering
                                                                ?.fuelLog?.nodes
                                                                ?.length > 0 &&
                                                            getFuelTotals(
                                                                event
                                                                    ?.eventType_RefuellingBunkering
                                                                    ?.fuelLog
                                                                    ?.nodes,
                                                            )}
                                                    </div>
                                                    {event?.eventCategory ===
                                                        'Tasking' &&
                                                        event?.eventType_Tasking
                                                            ?.type ===
                                                            'TaskingStartUnderway' &&
                                                        event?.eventType_Tasking
                                                            ?.status && (
                                                            <div
                                                                className={`${event?.eventType_Tasking?.status === 'Open' ? 'text-bright-turquoise-600' : ''} pr-2`}>
                                                                {
                                                                    event
                                                                        ?.eventType_Tasking
                                                                        ?.status
                                                                }
                                                            </div>
                                                        )}
                                                </div>
                                            </AccordionTrigger>
                                            <AccordionContent className="pb-4">
                                                {currentEventType &&
                                                    currentEvent &&
                                                    currentEvent.id ===
                                                        event.id && (
                                                        <>
                                                            {currentEventType.value ===
                                                                'VesselRescue' && (
                                                                <VesselRescue
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    geoLocations={
                                                                        geoLocations
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'HumanRescue' && (
                                                                <PersonRescue
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    geoLocations={
                                                                        geoLocations
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'RestrictedVisibility' && (
                                                                <RestrictedVisibility
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    members={
                                                                        crewMembers
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'BarCrossing' && (
                                                                <BarCrossing
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    members={
                                                                        crewMembers
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                />
                                                            )}
                                                            {(currentEventType.value ===
                                                                'PassengerArrival' ||
                                                                currentEventType.value ===
                                                                    'PassengerDeparture' ||
                                                                currentEventType.value ===
                                                                    'WaterTaxiService' ||
                                                                currentEventType.value ===
                                                                    'ScheduledPassengerService') && (
                                                                //TODO: update this form
                                                                <PassengerDropFacility
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    geoLocations={
                                                                        geoLocations
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    type={
                                                                        currentEventType.value
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    previousDropEvent={previousDropEvent(
                                                                        currentEvent,
                                                                    )}
                                                                    vessel={
                                                                        vessel
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    fuelLogs={
                                                                        fuelLogs
                                                                    }
                                                                />
                                                            )}
                                                            {(currentEventType.value ===
                                                                'TaskingStartUnderway' ||
                                                                currentEventType.value ===
                                                                    'TaskingOnScene' ||
                                                                currentEventType.value ===
                                                                    'TaskingOnTow' ||
                                                                currentEventType.value ===
                                                                    'TaskingPaused' ||
                                                                currentEventType.value ===
                                                                    'TaskingResumed' ||
                                                                currentEventType.value ===
                                                                    'TaskingComplete') && (
                                                                <Tasking
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    geoLocations={
                                                                        geoLocations
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    type={
                                                                        currentEventType.value
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    previousDropEvent={mainTaskingEvent(
                                                                        currentEvent,
                                                                    )}
                                                                    vessel={
                                                                        vessel
                                                                    }
                                                                    members={
                                                                        crewMembers
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    fuelLogs={
                                                                        fuelLogs
                                                                    }
                                                                />
                                                            )}
                                                            {permissions &&
                                                                hasPermission(
                                                                    'RECORD_TRAINING',
                                                                    permissions,
                                                                ) && (
                                                                    <>
                                                                        {currentEventType.value ===
                                                                            'CrewTraining' && (
                                                                            <CrewTrainingEvent
                                                                                offline={
                                                                                    offline
                                                                                }
                                                                                vesselId={
                                                                                    +vesselID
                                                                                }
                                                                                trainingTypeId={
                                                                                    0
                                                                                }
                                                                                currentTrip={
                                                                                    currentTrip
                                                                                }
                                                                                updateTripReport={
                                                                                    updateTripReport
                                                                                }
                                                                                selectedEvent={
                                                                                    currentEvent
                                                                                }
                                                                                tripReport={
                                                                                    tripReport
                                                                                }
                                                                                closeModal={
                                                                                    handleSetCurrentEventType
                                                                                }
                                                                                crewMembers={
                                                                                    crewMembers
                                                                                }
                                                                                masterID={
                                                                                    masterID
                                                                                }
                                                                                logBookConfig={
                                                                                    logBookConfig
                                                                                }
                                                                                vessels={
                                                                                    vessels
                                                                                }
                                                                                locked={
                                                                                    locked ||
                                                                                    !edit_tripActivity
                                                                                }
                                                                                logBookStartDate={
                                                                                    logBookStartDate
                                                                                }
                                                                            />
                                                                        )}
                                                                    </>
                                                                )}
                                                            {currentEventType.value ===
                                                                'EventSupernumerary' && (
                                                                <SupernumeraryEvent
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'RefuellingBunkering' && (
                                                                <RefuellingBunkering
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    mainFuelLogs={
                                                                        fuelLogs
                                                                    }
                                                                />
                                                            )}

                                                            {/* Restored missing items that were initially unavailable or missing */}

                                                            {currentEventType.value ===
                                                                'TripUpdate' && (
                                                                <TripUpdate
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'PilotTransfer' && (
                                                                <PilotTransfer
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'InfringementNotice' && (
                                                                <InfringementNotices
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    crewMembers={
                                                                        crewMembers
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    visibility={
                                                                        // selectedRow ===
                                                                        //     event.id &&
                                                                        currentEventType &&
                                                                        currentEvent
                                                                    }
                                                                />
                                                            )}
                                                            {currentEventType.value ===
                                                                'IncidentRecord' && (
                                                                <IncidentRecordForm
                                                                    id={
                                                                        currentEvent?.incidentRecordID ||
                                                                        0
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    inLogbook={
                                                                        true
                                                                    }
                                                                    selectedEvent={
                                                                        currentEvent
                                                                    }
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                />
                                                            )}

                                                            {/* ---------------------------------------------- */}
                                                        </>
                                                    )}
                                            </AccordionContent>
                                        </AccordionItem>
                                    )
                                },
                            )}
                            {currentTrip?.tripReport_Stops?.nodes.map(
                                (event: any, index: number) => {
                                    return (
                                        <AccordionItem
                                            key={index + '_stops'}
                                            value={`stop_${event.id}`}>
                                            <AccordionTrigger
                                                onClick={handleStopAccordionItemClick(
                                                    event.id,
                                                )}>
                                                <div className="flex items-center justify-between w-full">
                                                    <div className="text-left">
                                                        {getStopDisplayText(
                                                            event,
                                                        )}
                                                    </div>
                                                </div>
                                            </AccordionTrigger>
                                            <AccordionContent className="pb-4">
                                                {currentEventType &&
                                                    currentStop &&
                                                    currentStop.id ===
                                                        event.id && (
                                                        <>
                                                            {currentEventType.value ===
                                                                'PassengerVehiclePickDrop' && (
                                                                <PassengerVehiclePickDrop
                                                                    key={`pvpd-${event.id}`}
                                                                    offline={
                                                                        offline
                                                                    }
                                                                    geoLocations={
                                                                        geoLocations
                                                                    }
                                                                    updateTripReport={
                                                                        updateTripReport
                                                                    }
                                                                    currentTrip={
                                                                        currentTrip
                                                                    }
                                                                    selectedEvent={
                                                                        currentStop
                                                                    }
                                                                    tripReport={
                                                                        tripReport
                                                                    }
                                                                    closeModal={
                                                                        handleSetCurrentEventType
                                                                    }
                                                                    type={
                                                                        currentEventType.value
                                                                    }
                                                                    logBookConfig={
                                                                        logBookConfig
                                                                    }
                                                                    members={
                                                                        crewMembers
                                                                    }
                                                                    locked={
                                                                        locked ||
                                                                        !edit_tripActivity
                                                                    }
                                                                    tripReport_Stops={
                                                                        tripReport_Stops
                                                                    }
                                                                    setTripReport_Stops={
                                                                        setTripReport_Stops
                                                                    }
                                                                    displayDangerousGoods={
                                                                        displayDangerousGoodsPvpd
                                                                    }
                                                                    setDisplayDangerousGoods={
                                                                        setDisplayDangerousGoodsPvpd
                                                                    }
                                                                    displayDangerousGoodsSailing={
                                                                        displayDangerousGoodsPvpdSailing
                                                                    }
                                                                    setDisplayDangerousGoodsSailing={
                                                                        setDisplayDangerousGoodsPvpdSailing
                                                                    }
                                                                    allPVPDDangerousGoods={
                                                                        allPVPDDangerousGoods
                                                                    }
                                                                    setAllPVPDDangerousGoods={
                                                                        setAllPVPDDangerousGoods
                                                                    }
                                                                    selectedDGR={
                                                                        selectedDGRPVPD
                                                                    }
                                                                    setSelectedDGR={
                                                                        setSelectedDGRPVPD
                                                                    }
                                                                />
                                                            )}
                                                        </>
                                                    )}
                                            </AccordionContent>
                                        </AccordionItem>
                                    )
                                },
                            )}
                        </Accordion>
                    )}
                </>
            ) : null}
            <div className="flex justify-start gap-2">
                {/* {events && !currentEvent && !currentStop ? ( */}
                <Label position="left" className="w-full" label="Activity Type">
                    {activityTypeOptions.length > 0 ? (
                        <Combobox
                            id="task-assigned"
                            options={activityTypeOptions}
                            value={currentEventType}
                            onChange={handleEventChange}
                            title="Activity Type"
                            placeholder="Activity Type"
                        />
                    ) : (
                        // Failsafe - in case the activity types are not loaded.
                        <div className="flex items-center">
                            <Button
                                variant="outline"
                                onClick={fetchActivityTypes}>
                                Refresh activity types
                            </Button>
                        </div>
                    )}
                </Label>
                {/* ) : null} */}
                {/* <Button
                    className="justify-self-start"
                    disabled={locked || !edit_tripActivity}
                    onClick={() => {
                        if (!edit_tripActivity) {
                            toast.error(
                                'You do not have permission to record activity',
                            )
                            return
                        }
                        // setOpenEventModal(true)
                        setCurrentEvent(false)
                        setCurrentStop(false)
                        setTripReport_Stops(false)
                        setDisplayDangerousGoodsPvpd(false)
                        setDisplayDangerousGoodsPvpdSailing(null)
                        fetchActivityTypes()
                    }}>
                    Record activity
                </Button> */}
            </div>
            {currentEventType && !currentEvent && !currentStop && (
                <>
                    {currentEventType.value === 'VesselRescue' && (
                        <>
                            <VesselRescue
                                offline={offline}
                                geoLocations={geoLocations}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                locked={locked || !edit_tripActivity}
                            />
                        </>
                    )}
                    {currentEventType.value === 'HumanRescue' && (
                        <>
                            <PersonRescue
                                offline={offline}
                                geoLocations={geoLocations}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                locked={locked || !edit_tripActivity}
                            />
                        </>
                    )}
                    {currentEventType.value === 'RestrictedVisibility' && (
                        <>
                            <RestrictedVisibility
                                offline={offline}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                logBookConfig={logBookConfig}
                                locked={locked || !edit_tripActivity}
                                members={crewMembers}
                            />
                        </>
                    )}
                    {currentEventType.value === 'BarCrossing' && (
                        <>
                            <BarCrossing
                                offline={offline}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                members={crewMembers}
                                closeModal={handleSetCurrentEventType}
                                logBookConfig={logBookConfig}
                                locked={locked || !edit_tripActivity}
                            />
                        </>
                    )}
                    {(currentEventType.value === 'PassengerArrival' ||
                        currentEventType.value === 'PassengerDeparture' ||
                        currentEventType.value === 'WaterTaxiService' ||
                        currentEventType.value ===
                            'ScheduledPassengerService') && (
                        <>
                            <PassengerDropFacility
                                offline={offline}
                                geoLocations={geoLocations}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                type={currentEventType.value}
                                logBookConfig={logBookConfig}
                                previousDropEvent={previousDropEvent(
                                    currentEvent,
                                )}
                                vessel={vessel}
                                locked={locked || !edit_tripActivity}
                                fuelLogs={fuelLogs}
                            />
                        </>
                    )}
                    {(currentEventType.value === 'TaskingStartUnderway' ||
                        currentEventType.value === 'TaskingOnScene' ||
                        currentEventType.value === 'TaskingOnTow' ||
                        currentEventType.value === 'TaskingPaused' ||
                        currentEventType.value === 'TaskingResumed' ||
                        currentEventType.value === 'TaskingComplete') && (
                        <>
                            <Tasking
                                offline={offline}
                                geoLocations={geoLocations}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                type={currentEventType.value}
                                logBookConfig={logBookConfig}
                                previousDropEvent={mainTaskingEvent(
                                    currentEvent,
                                )}
                                vessel={vessel}
                                members={crewMembers}
                                locked={locked || !edit_tripActivity}
                                fuelLogs={fuelLogs}
                            />
                        </>
                    )}
                    {permissions &&
                        hasPermission('RECORD_TRAINING', permissions) && (
                            <>
                                {currentEventType.value === 'CrewTraining' && (
                                    <>
                                        <CrewTrainingEvent
                                            offline={offline}
                                            vesselId={+vesselID}
                                            trainingTypeId={0}
                                            currentTrip={currentTrip}
                                            updateTripReport={updateTripReport}
                                            selectedEvent={currentEvent}
                                            tripReport={tripReport}
                                            closeModal={
                                                handleSetCurrentEventType
                                            }
                                            crewMembers={crewMembers}
                                            masterID={masterID}
                                            logBookConfig={logBookConfig}
                                            vessels={vessels}
                                            locked={
                                                locked || !edit_tripActivity
                                            }
                                            logBookStartDate={logBookStartDate}
                                        />
                                    </>
                                )}
                            </>
                        )}
                    {currentEventType.value === 'EventSupernumerary' && (
                        <>
                            <SupernumeraryEvent
                                offline={offline}
                                logBookConfig={logBookConfig}
                                locked={locked || !edit_tripActivity}
                                closeModal={handleSetCurrentEventType}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                tripReport={tripReport}
                                selectedEvent={currentEvent}
                            />
                        </>
                    )}
                    {currentEventType.value === 'PassengerVehiclePickDrop' && (
                        <>
                            <PassengerVehiclePickDrop
                                offline={offline}
                                geoLocations={geoLocations}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentStop}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                type={currentEventType.value}
                                logBookConfig={logBookConfig}
                                members={crewMembers}
                                locked={locked || !edit_tripActivity}
                                tripReport_Stops={tripReport_Stops}
                                setTripReport_Stops={setTripReport_Stops}
                                displayDangerousGoods={
                                    displayDangerousGoodsPvpd
                                }
                                setDisplayDangerousGoods={
                                    setDisplayDangerousGoodsPvpd
                                }
                                displayDangerousGoodsSailing={
                                    displayDangerousGoodsPvpdSailing
                                }
                                setDisplayDangerousGoodsSailing={
                                    setDisplayDangerousGoodsPvpdSailing
                                }
                                allPVPDDangerousGoods={allPVPDDangerousGoods}
                                setAllPVPDDangerousGoods={
                                    setAllPVPDDangerousGoods
                                }
                                selectedDGR={selectedDGRPVPD}
                                setSelectedDGR={setSelectedDGRPVPD}
                            />
                        </>
                    )}
                    {currentEventType.value === 'RefuellingBunkering' && (
                        <>
                            <RefuellingBunkering
                                offline={offline}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                logBookConfig={logBookConfig}
                                locked={locked || !edit_tripActivity}
                                mainFuelLogs={fuelLogs}
                            />
                        </>
                    )}
                    {currentEventType.value === 'TripUpdate' && (
                        <>
                            <TripUpdate
                                offline={offline}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                locked={locked || !edit_tripActivity}
                            />
                        </>
                    )}
                    {/*currentEventType.value === 'PilotTransfer' && (
                        <>
                            <PilotTransfer
                                offline={offline}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                logBookConfig={logBookConfig}
                                locked={locked || !edit_tripActivity}
                            />
                        </>
                    )*/}
                    {currentEventType.value === 'InfringementNotice' && (
                        <>
                            <InfringementNotices
                                offline={offline}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                selectedEvent={currentEvent}
                                tripReport={tripReport}
                                closeModal={handleSetCurrentEventType}
                                crewMembers={crewMembers}
                                locked={locked || !edit_tripActivity}
                                visibility={
                                    currentEventType &&
                                    !currentEvent &&
                                    !currentStop
                                }
                            />
                        </>
                    )}
                    {currentEventType.value === 'IncidentRecord' && (
                        <>
                            <IncidentRecordForm
                                id={currentEvent?.incidentRecordID || 0}
                                currentTrip={currentTrip}
                                updateTripReport={updateTripReport}
                                closeModal={handleSetCurrentEventType}
                                inLogbook={true}
                                selectedEvent={currentEvent}
                                offline={offline}
                                tripReport={tripReport}
                            />
                        </>
                    )}
                </>
            )}
            {currentTrip.tripReportScheduleID > 0 && (
                <RadioLogsScheduleService
                    open={displayRadioLogs}
                    setOpen={setDisplayRadioLogs}
                    currentTrip={currentTrip}
                />
            )}
        </div>
    )
}
