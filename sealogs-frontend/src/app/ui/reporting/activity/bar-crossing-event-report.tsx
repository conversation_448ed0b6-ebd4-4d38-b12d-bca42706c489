'use client'

import { useCallback, useEffect, useMemo } from 'react'
import { IEventReportProps } from './main'
import ExportButton from '../export-button'
import { GET_BAR_CROSSING_EVENT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

interface IReportItem {
    vesselName: string
    date: Date
    startTime?: string
    startLocation?: string
    endTime?: string
    riskAnalysisCompleted: boolean
    comments?: string
}

const tableHeadings: string[] = [
    'Vessel',
    'Date',
    'Start time event',
    'Start location of event',
    'Time event ends',
    'Risk analysis completed',
    'Comments',
]

export default function BarCrossingEventReport({
    dateRange,
    vessels,
    lastGenerated,
}: IEventReportProps) {
    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_BAR_CROSSING_EVENT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('GET_BAR_CROSSING_EVENT_ENTRIES error', error)
            },
        },
    )

    const generateReport = useCallback(() => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (vessels.length > 0) {
            filter['vehicleID'] = {
                in: vessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }, [dateRange, vessels])

    useEffect(() => {
        if (lastGenerated != null) {
            generateReport()
        }
    }, [lastGenerated])

    const stripHtmlTags = (value: string): string => {
        return value.replace(/<[^>]*>/g, '')
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach(function (item: any) {
            if (item.state !== 'Locked') {
                return
            }

            if (item.vehicle.id == 0) {
                return
            }

            if (item.logBookEntrySections.nodes.length === 0) {
                return
            }

            const logBookEntrySections = item.logBookEntrySections.nodes

            const tripEvents = logBookEntrySections.reduce(
                (acc: any, current: any) => [
                    ...acc,
                    ...current.tripEvents.nodes,
                ],
                [],
            )

            if (tripEvents.length === 0) {
                return
            }

            tripEvents.forEach((tripEvent: any) => {
                const event = tripEvent.eventType_BarCrossing

                if (event.created === null) {
                    return
                }

                const riskAnalysisCompleted =
                    event.crewBriefing &&
                    event.lifeJackets &&
                    event.lookoutPosted &&
                    event.stability &&
                    event.stopAssessPlan &&
                    event.waterTightness &&
                    event.weather

                const reportItem: IReportItem = {
                    vesselName: item.vehicle.title,
                    date: new Date(event.created),
                    startTime: event.time,
                    endTime: event.timeCompleted,
                    startLocation: event.geoLocation.title,
                    riskAnalysisCompleted,
                    comments: event.report,
                }

                reportItems.push(reportItem)
            })
        })

        return reportItems
    }, [called, data, loading])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = [
            [
                'vessel',
                'date',
                'start time event',
                'start location of event',
                'time event ends',
                'risk analysis completed',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                dayjs(item.date).format('DD/MM/YYYY'),
                item.startTime ?? 'N/A',
                item.startLocation ?? 'N/A',
                item.endTime ?? 'N/A',
                item.riskAnalysisCompleted ? 'YES' : 'NO',
                item.comments ? stripHtmlTags(item.comments) : '',
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            [
                'Vessel',
                'Date',
                'Start time event',
                'Start location of event',
                'Time event ends',
                'Risk analysis completed',
                'Comments',
            ],
        ]

        const body: any = reportData.map((item) => {
            return [
                item.vesselName,
                dayjs(item.date).format('DD/MM/YY'),
                item.startTime ?? 'N/A',
                item.startLocation ?? 'N/A',
                item.endTime ?? 'N/A',
                item.riskAnalysisCompleted ? 'YES' : 'NO',
                item.comments ? stripHtmlTags(item.comments) : '',
            ]
        })

        exportPdfTable({
            headers,
            body,
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <ExportButton
                onDownloadCsv={downloadCsv}
                onDownloadPdf={downloadPdf}
            />
            <Table>
                <TableHeader>
                    <TableRow>
                        {tableHeadings.map((heading) => (
                            <TableHead key={heading}>{heading}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableContent
                        reportData={reportData}
                        isLoading={called && loading}
                    />
                </TableBody>
            </Table>
        </div>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    No Data Available
                </TableCell>
            </TableRow>
        )
    }

    return reportData.map((element: IReportItem, index: number) => {
        return (
            <TableRow
                key={'report-item-' + index}
                className={`group border-b hover: `}>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">{element.vesselName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {dayjs(element.date).format('DD/MM/YY')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {element.startTime ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">
                        {element.startLocation ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">
                        {element.endTime ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">
                        {element.riskAnalysisCompleted ? 'YES' : 'NO'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div
                        className=" inline-block"
                        dangerouslySetInnerHTML={{
                            __html: element.comments ?? '',
                        }}
                    />
                </TableCell>
            </TableRow>
        )
    })
}
