'use client'

import Filter from '@/components/filter'
import { formatDate } from '@/app/helpers/dateHelper'
import { userHasRescueVessel } from '@/app/lib/actions'
import {
    GetTripIdsByVesselID,
    TripReport_LogBookEntrySection_Brief,
} from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { max, min } from 'lodash'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'
import {
    Button,
    Card,
    CardContent,
    H1,
    H3,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'
import { CheckCircle } from 'lucide-react'

const headings = ['Date', 'Start', 'End', 'Fuel Start', 'Fuel End', 'Fuel used']

export default function FuelAnalysis() {
    const router = useRouter()
    const [vesselID, setVesselID] = useState('')
    const [dateRange, setDateRange] = useState<any>(false)
    const [hasRescueVessel, setHasRescueVessel] = useState(false)
    const [fuelReport, setFuelReport] = useState([])

    const handleFilterChange = (filters: any) => {
        if (filters.type === 'vessel' && filters.data?.value) {
            setVesselID(filters.data?.value)
        }
        if (filters.type === 'dateRange') {
            setDateRange(filters.data)
        }
    }

    useEffect(() => {
        if (vesselID) {
            loadTripIdsByVesselID({
                variables: {
                    id: vesselID,
                },
            })
        }
    }, [vesselID])

    const [loadTripIdsByVesselID] = useLazyQuery(GetTripIdsByVesselID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            var tripIds: string[] = []
            const data = response.readOneVessel.logBookEntries.nodes?.map(
                (entry: any) =>
                    entry.logBookEntrySections.nodes?.map((section: any) => {
                        tripIds.push(section.id)
                    }),
            )
            getSectionTripReport_LogBookEntrySection({
                variables: {
                    id: tripIds,
                },
            })
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection_Brief,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                var fuelReport: any = []
                data.forEach((entry: any) => {
                    var maxFuelLevel: any = 0
                    entry.tripEvents.nodes.forEach((event: any) => {
                        var fuelLevel = 0
                        if (event.eventCategory === 'PassengerDropFacility') {
                            fuelLevel =
                                event.eventType_PassengerDropFacility
                                    .fuelLevel != 0
                                    ? event.eventType_PassengerDropFacility
                                          .fuelLevel
                                    : fuelLevel
                        }
                        if (event.eventCategory === 'Tasking') {
                            fuelLevel =
                                event.eventType_Tasking.fuelLevel != 0
                                    ? event.eventType_Tasking.fuelLevel
                                    : fuelLevel
                        }
                        if (event.eventCategory === 'PassengerDropFacility') {
                            fuelLevel =
                                event.eventType_PassengerDropFacility
                                    .fuelLevel != 0
                                    ? event.eventType_PassengerDropFacility
                                          .fuelLevel
                                    : fuelLevel
                        }
                        maxFuelLevel = max([+maxFuelLevel, +fuelLevel])
                    })
                    var minFuelLevel: any = maxFuelLevel
                    entry.tripEvents.nodes.forEach((event: any) => {
                        var fuelLevel = 0
                        if (event.eventCategory === 'PassengerDropFacility') {
                            fuelLevel =
                                event.eventType_PassengerDropFacility
                                    .fuelLevel != 0
                                    ? event.eventType_PassengerDropFacility
                                          .fuelLevel
                                    : fuelLevel
                        }
                        if (event.eventCategory === 'Tasking') {
                            fuelLevel =
                                event.eventType_Tasking.fuelLevel != 0
                                    ? event.eventType_Tasking.fuelLevel
                                    : fuelLevel
                        }
                        if (event.eventCategory === 'PassengerDropFacility') {
                            fuelLevel =
                                event.eventType_PassengerDropFacility
                                    .fuelLevel != 0
                                    ? event.eventType_PassengerDropFacility
                                          .fuelLevel
                                    : fuelLevel
                        }
                        minFuelLevel =
                            minFuelLevel === 0
                                ? +fuelLevel
                                : min([+minFuelLevel, +fuelLevel])
                    })
                    if (maxFuelLevel > 0 || minFuelLevel > 0) {
                        fuelReport.push({
                            id: entry.id,
                            maxFuelLevel: maxFuelLevel,
                            minFuelLevel: minFuelLevel,
                            fromLocation: entry.fromLocation,
                            toLocation: entry.toLocation,
                            arrivalTime: entry.arriveTime,
                            departureTime: entry.departTime,
                            created: entry.created,
                        })
                    }
                })
                setFuelReport(fuelReport)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const filterByDate = (entry: any) => {
        if (dateRange) {
            return (
                dayjs(entry.created).isAfter(dayjs(dateRange.startDate)) &&
                dayjs(entry.created).isBefore(dayjs(dateRange.endDate))
            )
        }
        return true
    }

    userHasRescueVessel(setHasRescueVessel)

    if (!hasRescueVessel) {
        return <div></div>
    }

    return (
        <>
            <ListHeader
                title="Fuel Analysis"
                actions={
                    <div className="flex gap-2.5">
                        <Button
                            variant={'back'}
                            onClick={() => router.push('/reporting')}>
                            Back
                        </Button>
                    </div>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter onChange={handleFilterChange} />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {headings.map((header) => (
                                    <TableHead key={header}>{header}</TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {fuelReport.length == 0 ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={headings.length}
                                        className="text-center h-32">
                                        No Data Available
                                    </TableCell>
                                </TableRow>
                            ) : (
                                fuelReport
                                    .filter((entry: any) => filterByDate(entry))
                                    .map((entry: any) => (
                                        <TableRow key={entry.id}>
                                            <TableCell>
                                                {formatDate(entry.created)}
                                            </TableCell>
                                            <TableCell>
                                                {entry.fromLocation?.title
                                                    ? entry.fromLocation.title +
                                                      ' - '
                                                    : ''}
                                                {entry.departureTime}
                                            </TableCell>
                                            <TableCell>
                                                {entry.toLocation?.title
                                                    ? entry.toLocation.title +
                                                      ' - '
                                                    : ''}
                                                {entry.arrivalTime}
                                            </TableCell>
                                            <TableCell>
                                                {entry.maxFuelLevel}
                                            </TableCell>
                                            <TableCell>
                                                {entry.minFuelLevel}
                                            </TableCell>
                                            <TableCell>
                                                {entry.maxFuelLevel -
                                                    entry.minFuelLevel}
                                            </TableCell>
                                        </TableRow>
                                    ))
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}
