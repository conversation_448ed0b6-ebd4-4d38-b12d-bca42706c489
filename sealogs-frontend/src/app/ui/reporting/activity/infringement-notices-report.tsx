'use client'

import { useCallback, useEffect, useMemo } from 'react'
import { IEventReportProps } from './main'
import ExportButton from '../export-button'
import { GET_INFRINGEMENT_NOTICES_EVENT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import {
    getInfringementFieldLabel,
    infringementOptions,
} from '@/app/lib/infringement-notices-options'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

interface IReportItem {
    date: Date
    time: string
    location?: string
    vesselName: string
    vesselRegistration?: string
    ownerFullName?: string
    ownerDOB?: Date | null
    ownerOccupation?: string
    ownerAddress?: string
    ownerPhoneNumber?: string
    ownerEmail?: string
    infringementIncurred: string
    checkedInfringements: string[]
    comments?: string
}

const tableHeadings: string[] = [
    'Date',
    'Time',
    'Location',
    'Vessel Name',
    'Vessel registration',
    'Owner full name',
    'Owner date of birth',
    'Owner occupation.',
    'Owner address',
    'Owner phone number',
    'Owner Email Address',
    'Infringement incurred',
    'List of checked infringements',
    'Comments',
]

export default function InfringementNoticesReport({
    dateRange,
    vessels,
    lastGenerated,
}: IEventReportProps) {
    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_INFRINGEMENT_NOTICES_EVENT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_INFRINGEMENT_NOTICES_EVENT_ENTRIES error',
                    error,
                )
            },
        },
    )

    const generateReport = useCallback(() => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (vessels.length > 0) {
            filter['vehicleID'] = {
                in: vessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }, [dateRange, vessels])

    useEffect(() => {
        if (lastGenerated != null) {
            generateReport()
        }
    }, [lastGenerated])

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach(function (item: any) {
            if (item.state !== 'Locked') {
                return
            }

            if (item.vehicle.id == 0) {
                return
            }

            if (item.logBookEntrySections.nodes.length === 0) {
                return
            }

            const logBookEntrySections = item.logBookEntrySections.nodes

            const tripEvents = logBookEntrySections.reduce(
                (acc: any, current: any) => [
                    ...acc,
                    ...current.tripEvents.nodes,
                ],
                [],
            )

            if (tripEvents.length === 0) {
                return
            }

            tripEvents.forEach((tripEvent: any) => {
                const event = tripEvent.infringementNotice

                if (event.created === null) {
                    return
                }

                const infringementData = JSON.parse(event.infringementData)
                const infringementIncurred: string[] =
                    infringementData?.infringementUsed ?? []

                if (infringementIncurred.length === 0) {
                    return
                }

                const eventDate = new Date(event.created)

                infringementIncurred.forEach((infringement) => {
                    const infringementItemsChecked: string[] = (
                        infringementData[infringement] as Array<string>
                    )
                        .map((key) => {
                            return getInfringementFieldLabel(infringement, key)
                        })
                        .filter((item) => item !== null)

                    const infringementLabel = Object.hasOwn(
                        infringementOptions,
                        infringement,
                    )
                        ? infringementOptions[infringement]
                        : 'N/A'

                    const reportItem: IReportItem = {
                        date: eventDate,
                        time: event.time ?? dayjs(eventDate).format('HH:mm:ss'),
                        location: event.geoLocation.title ?? 'N/A',
                        vesselName: item.vehicle.title,
                        vesselRegistration: item.vehicle.registration,
                        ownerFullName: event.ownerFullName,
                        ownerDOB: event.ownerDOB
                            ? new Date(event.ownerDOB)
                            : null,
                        ownerOccupation: event.ownerOccupation,
                        ownerAddress: event.ownerAddress,
                        ownerPhoneNumber: event.ownerPhone,
                        ownerEmail: event.ownerEmail,
                        infringementIncurred: infringementLabel,
                        checkedInfringements: infringementItemsChecked,
                        comments: tripEvent.notes,
                    }

                    reportItems.push(reportItem)
                })
            })
        })

        return reportItems
    }, [called, loading, data])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = [
            [
                'date',
                'time',
                'location',
                'vessel name',
                'vessel registration',
                'owner full name',
                'owner date of birth',
                'owner occupation.',
                'owner address',
                'owner phone number',
                'owner email address',
                'infringement incurred',
                'list of checked infringements',
                'comments',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                dayjs(item.date).format('DD/MM/YYYY'),
                item.time,
                item.location ?? 'N/A',
                item.vesselName,
                item.vesselRegistration ?? 'N/A',
                item.ownerFullName ?? 'N/A',
                item.ownerDOB
                    ? dayjs(item.ownerDOB).format('DD/MM/YYYY')
                    : 'N/A',
                item.ownerOccupation ?? 'N/A',
                item.ownerAddress ?? 'N/A',
                item.ownerPhoneNumber ?? 'N/A',
                item.ownerEmail ?? 'N/A',
                item.infringementIncurred,
                item.checkedInfringements.join('/'),
                item.comments ?? 'N/A',
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }
        const headers: any = [
            [
                //page 1
                'Date',
                'Time',
                'Location',
                'Vessel Name',
                'Vessel registration',
                'Owner full name',
                'Owner date of birth',
                'Owner occupation',
                'Owner address',

                //page 1.1
                'Date',
                'Time',
                'Location',
                'Vessel Name',
                'Vessel registration',
                'Owner full name',
                'Owner phone number',
                'Owner Email Address',

                //page 2
                'Date',
                'Time',
                'Location',
                'Vessel Name',
                'Vessel registration',
                'Infringement incurred',
                'Comments',

                //page 3
                'Date',
                'Time',
                'Location',
                'Vessel Name',
                'Vessel registration',
                'Infringement incurred',
                'List of checked infringements',
            ],
        ]

        const body: any = reportData.map((item) => {
            return [
                //page 1
                {
                    content: dayjs(item.date).format('DD/MM/YYYY'),
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.time,
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.location ?? 'N/A',
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.vesselName,
                    styles: {
                        cellWidth: 35,
                    },
                },
                {
                    content: item.vesselRegistration ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.ownerFullName ?? 'N/A',
                    styles: {
                        cellWidth: 32,
                    },
                },
                {
                    content: item.ownerDOB
                        ? dayjs(item.ownerDOB).format('DD/MM/YYYY')
                        : 'N/A',
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.ownerOccupation ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.ownerAddress ?? 'N/A',
                    styles: {
                        cellWidth: 40,
                    },
                },

                //page 1.1
                {
                    content: dayjs(item.date).format('DD/MM/YYYY'),
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.time,
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.location ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.vesselName,
                    styles: {
                        cellWidth: 35,
                    },
                },
                {
                    content: item.vesselRegistration ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.ownerFullName ?? 'N/A',
                    styles: {
                        cellWidth: 40,
                    },
                },
                {
                    content: item.ownerPhoneNumber ?? 'N/A',
                    styles: {
                        cellWidth: 40,
                    },
                },
                {
                    content: item.ownerEmail ?? 'N/A',
                    styles: {
                        cellWidth: 40,
                    },
                },

                //page 2
                {
                    content: dayjs(item.date).format('DD/MM/YYYY'),
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.time,
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.location ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.vesselName,
                    styles: {
                        cellWidth: 35,
                    },
                },
                {
                    content: item.vesselRegistration ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.infringementIncurred,
                    styles: {
                        cellWidth: 65,
                    },
                },
                {
                    content: item.comments ?? 'N/A',
                    styles: {
                        cellWidth: 70,
                    },
                },

                //page 3
                {
                    content: dayjs(item.date).format('DD/MM/YYYY'),
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.time,
                    styles: {
                        cellWidth: 25,
                    },
                },
                {
                    content: item.location ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.vesselName,
                    styles: {
                        cellWidth: 35,
                    },
                },
                {
                    content: item.vesselRegistration ?? 'N/A',
                    styles: {
                        cellWidth: 30,
                    },
                },
                {
                    content: item.infringementIncurred,
                    styles: {
                        cellWidth: 50,
                    },
                },
                {
                    content: item.checkedInfringements.join('; '),
                    styles: {
                        cellWidth: 82,
                    },
                },
            ]
        })

        exportPdfTable(
            {
                headers,
                body,
                userOptions: {
                    horizontalPageBreak: true,
                },
            },
            {
                orientation: 'landscape',
            },
        )
    }

    return (
        <div className="flex flex-col gap-4">
            <ExportButton
                onDownloadCsv={downloadCsv}
                onDownloadPdf={downloadPdf}
            />
            <Table>
                <TableHeader>
                    <TableRow className="overflow-x-auto">
                        {tableHeadings.map((heading) => (
                            <TableHead key={heading}>{heading}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableContent
                        reportData={reportData}
                        isLoading={called && loading}
                    />
                </TableBody>
            </Table>
        </div>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    No Data Available
                </TableCell>
            </TableRow>
        )
    }

    return reportData.map((element: IReportItem, index: number) => {
        return (
            <TableRow
                key={'report-item-' + index}
                className={`group border-b hover: `}>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block ">
                        {dayjs(element.date).format('DD/MM/YY')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block ">{element.time}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.location ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">{element.vesselName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.vesselRegistration ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.ownerFullName ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.ownerDOB
                            ? dayjs(element.ownerDOB).format('DD/MM/YY')
                            : 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.ownerOccupation ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.ownerAddress ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.ownerPhoneNumber ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.ownerEmail ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">
                        {element.infringementIncurred}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <ol className=" flex flex-col list-decimal">
                        {element.checkedInfringements.map((item) => {
                            return <li>{item}</li>
                        })}
                    </ol>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[7%]">
                    <div className=" inline-block">{element.comments}</div>
                </TableCell>
            </TableRow>
        )
    })
}
