'use client'

import { UpdateTripReport_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import { H4 } from '@/components/ui'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useMutation } from '@apollo/client'
import React, { useEffect, useState } from 'react'

export default function VOB({
    currentTrip,
    logBookConfig,
    offline = false,
}: {
    currentTrip: any
    logBookConfig: any
    offline?: boolean
}) {
    const [vehicleJoined, setVehicleJoined] = useState<number>(0)
    const [initialVessel, setInitialVessel] = useState<number>(
        parseInt(currentTrip?.totalVehiclesCarried) || 0,
    )
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    useEffect(() => {
        if (currentTrip) {
            const vehicleJoined =
                currentTrip?.tripReport_Stops?.nodes?.reduce(
                    (acc: number, stop: any) => {
                        const joined = parseInt(stop.vehiclesJoined) || 0
                        const departed = parseInt(stop.vehiclesDeparted) || 0
                        return acc + joined - departed
                    },
                    0,
                ) ?? 0
            setVehicleJoined(vehicleJoined)
            const totalVehiclesCarried =
                parseInt(currentTrip.totalVehiclesCarried) || 0
            setInitialVessel(totalVehiclesCarried)
        }
    }, [currentTrip])

    const displayField = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const handleVesselChange = async (vessel: any) => {
        if (offline) {
            await tripReportModel.save({
                id: currentTrip.id,
                totalVehiclesCarried: +vessel.target.value,
            })
        } else {
            updateTripReport_LogBookEntrySection({
                variables: {
                    input: {
                        id: currentTrip.id,
                        totalVehiclesCarried: +vessel.target.value,
                    },
                },
            })
        }
        setInitialVessel(parseInt(vessel.target.value) - vehicleJoined)
    }

    const handleVesselValueChange = (vessel: any) => {
        const value = parseInt(vessel.target.value) || 0
        const joined = vehicleJoined || 0
        setInitialVessel(Math.max(0, value - joined))
    }

    const [updateTripReport_LogBookEntrySection] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (data) => {},
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    return (
        <>
            <H4>VEHICLES ON BOARD</H4>
            {/* {displayField('PassengerVehiclePickDrop') && ( */}
            <Label label="Number of Vehicles" htmlFor="vob">
                <Input
                    id="vob"
                    name="vob"
                    type="number"
                    className="max-w-sm"
                    value={(vehicleJoined > 0
                        ? vehicleJoined + initialVessel
                        : initialVessel
                    ).toString()}
                    aria-describedby="pob-error"
                    required
                    min={Math.max(0, vehicleJoined || 0)}
                    onBlur={handleVesselChange}
                    onChange={handleVesselValueChange}
                />
            </Label>
            {/* )} */}
        </>
    )
}
