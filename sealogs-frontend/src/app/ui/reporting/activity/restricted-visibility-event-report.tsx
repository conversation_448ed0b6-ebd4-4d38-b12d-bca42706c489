'use client'

import { useCallback, useEffect, useMemo } from 'react'
import { IEventReportProps } from './main'
import ExportButton from '../export-button'
import { GET_RESTRICTED_VISIBILIY_EVENT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

interface IReportItem {
    vesselName: string
    date: Date
    startTime: string
    startLocation: string
    endTime: string
    endLocation: string
    estSafeSpeed: number | null
    actualSpeed: number | null
    sopCompleted: boolean
    comments?: string
}

const tableHeadings: string[] = [
    'Vessel',
    'Date',
    'Start time of event',
    'Start location of event',
    'End time of Event',
    'End location of event',
    'Safe speed estimated',
    'Actual speed observed',
    'SOPs completed',
    'Comments',
]

export default function RestrictedVisibilityEventReport({
    dateRange,
    vessels,
    lastGenerated,
}: IEventReportProps) {
    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_RESTRICTED_VISIBILIY_EVENT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_RESTRICTED_VISIBILIY_EVENT_ENTRIES error',
                    error,
                )
            },
        },
    )

    const generateReport = useCallback(() => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (vessels.length > 0) {
            filter['vehicleID'] = {
                in: vessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }, [dateRange, vessels])

    useEffect(() => {
        if (lastGenerated != null) {
            generateReport()
        }
    }, [lastGenerated])

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach(function (item: any) {
            if (item.state !== 'Locked') {
                return
            }

            if (item.vehicle.id == 0) {
                return
            }

            if (item.logBookEntrySections.nodes.length === 0) {
                return
            }

            const logBookEntrySections = item.logBookEntrySections.nodes

            const tripEvents = logBookEntrySections.reduce(
                (acc: any, current: any) => [
                    ...acc,
                    ...current.tripEvents.nodes,
                ],
                [],
            )

            if (tripEvents.length === 0) {
                return
            }

            tripEvents.forEach((tripEvent: any) => {
                const event = tripEvent.eventType_RestrictedVisibility

                if (event.created === null) {
                    return
                }

                const eventDate = new Date(event.created)

                const sopCompleted =
                    event.lookout &&
                    event.navLights &&
                    event.radarWatch &&
                    event.radioWatch &&
                    event.soundSignals

                const reportItem: IReportItem = {
                    vesselName: item.vehicle.title,
                    date: eventDate,
                    startTime: event.crossingTime ?? 'N/A',
                    startLocation: event.startLocation.title ?? 'N/A',
                    endTime: event.crossedTime ?? 'N/A',
                    endLocation: event.startLocation.title ?? 'N/A',
                    actualSpeed: event.approxSafeSpeed
                        ? Number(event.approxSafeSpeed)
                        : null,
                    estSafeSpeed: event.estSafeSpeed
                        ? Number(event.estSafeSpeed)
                        : null,
                    sopCompleted,
                    comments: event.report,
                }

                reportItems.push(reportItem)
            })
        })

        reportItems.sort((itemA, itemB) => {
            return itemA.date.getTime() - itemB.date.getTime()
        })

        return reportItems
    }, [called, loading, data])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = [
            [
                'vessel',
                'date',
                'start time of event',
                'start location of event',
                'end time of event',
                'end location of event',
                'safe speed estimated',
                'actual speed observed',
                'sops completed',
                'comments',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                dayjs(item.date).format('DD/MM/YYYY'),
                item.startTime,
                item.startLocation,
                item.endTime,
                item.endLocation,
                item.actualSpeed ? item.actualSpeed + '' : 'N/A',
                item.estSafeSpeed ? item.estSafeSpeed + '' : 'N/A',
                item.sopCompleted ? 'Yes' : 'No',
                item.comments ?? 'N/A',
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            [
                'Vessel',
                'Date',
                'Start time of event',
                'Start location of event',
                'End time of Event',
                'End location of event',
                'Safe speed estimated',
                'Actual speed observed',
                'SOPs completed',
                'Comments',
            ],
        ]

        const body: any = reportData.map((item) => {
            return [
                item.vesselName,
                dayjs(item.date).format('DD/M/YY'),
                item.startTime,
                item.startLocation,
                item.endTime,
                item.endLocation,
                item.actualSpeed ? item.actualSpeed + '' : 'N/A',
                item.estSafeSpeed ? item.estSafeSpeed + '' : 'N/A',
                item.sopCompleted ? 'Yes' : 'No',
                item.comments ?? 'N/A',
            ]
        })

        exportPdfTable({
            headers,
            body,
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <ExportButton
                onDownloadCsv={downloadCsv}
                onDownloadPdf={downloadPdf}
            />
            <Table>
                <TableHeader>
                    <TableRow>
                        {tableHeadings.map((heading) => (
                            <TableHead key={heading}>{heading}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableContent
                        reportData={reportData}
                        isLoading={called && loading}
                    />
                </TableBody>
            </Table>
        </div>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    No Data Available
                </TableCell>
            </TableRow>
        )
    }

    return reportData.map((element: IReportItem, index: number) => {
        return (
            <TableRow
                key={'report-item-' + index}
                className={`group border-b  hover: `}>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.vesselName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {dayjs(element.date).format('DD/M/YY')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">{element.startTime}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.startLocation}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.endTime}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.endLocation}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">
                        {element.estSafeSpeed ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">
                        {element.actualSpeed ?? 'N/A'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">
                        {element.sopCompleted ? 'YES' : 'NO'}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.comments}</div>
                </TableCell>
            </TableRow>
        )
    })
}
