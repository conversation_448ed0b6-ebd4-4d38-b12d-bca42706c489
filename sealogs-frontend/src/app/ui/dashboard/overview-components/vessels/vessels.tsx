'use client'
import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { useLazyQuery } from '@apollo/client'
import { createColumns, DataTable } from '@/components/filteredTable'
import VesselIcon from '../../../vessels/vesel-icon'
import { Vessel } from '../../../../../../types/vessel'
import { SealogsVesselsIcon } from '@/app/lib/icons'
import VesselStatusChart from '../vessel-status-chart'
import { subMonths } from 'date-fns'
import { H1 } from '@/components/ui/typography'
import { TableActionColumn } from '../../../vessels/components/table-action-column'
import { ReadDashboardData, ReadOneSeaLogsMember, ReadVessels } from './queries'
export default function Vessels() {
    const [isLoading, setIsLoading] = useState(true)
    const [vesselList, setVesselList] = useState<Vessel[]>([])
    const [filteredVesselList, setFilteredVesselList] = useState<Vessel[]>([])
    const [currentDepartment, setCurrentDepartment] = useState<any>(false)
    // const [displayEditStatus, setDisplayEditStatus] = useState(false)
    // const [vessel, setVessel] = useState<any>(false)

    const [querySeaLogsMembers] = useLazyQuery(ReadOneSeaLogsMember, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneSeaLogsMember
            if (data) {
                localStorage.setItem(
                    'timezone',
                    data.client.hqAddress.timeZone || 'Pacific/Auckland',
                )
                const departmentFlatMap = data.departments.nodes.flatMap(
                    (department: any) => department.basicComponents.nodes,
                )
                setCurrentDepartment(departmentFlatMap)
                if (departmentFlatMap.length === 0) {
                    setCurrentDepartment(true)
                }
            }
        },
        onError: (error: any) => {
            console.error('querySeaLogsMembers error', error)
        },
    })

    useEffect(() => {
        querySeaLogsMembers({
            variables: {
                filter: { id: { eq: +(localStorage.getItem('userId') ?? 0) } },
            },
        })
    }, [])

    useEffect(() => {
        if (currentDepartment && vesselList) {
            if (
                currentDepartment === true ||
                localStorage.getItem('useDepartment') !== 'true'
            ) {
                setFilteredVesselList(vesselList)
            } else {
                setFilteredVesselList(
                    vesselList.filter((vessel: any) =>
                        currentDepartment.some(
                            (department: any) => department.id === vessel.id,
                        ),
                    ),
                )
                setFilteredVesselList(vesselList)
            }
        }
    }, [currentDepartment, vesselList])

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter(
            (vessel: any) => vessel.showOnDashboard,
        )
        setVesselList(activeVessels)
    }

    const [queryVessels] = useLazyQuery(ReadDashboardData, {
        fetchPolicy: 'no-cache',
        onCompleted: async (queryVesselResponse: any) => {
            if (
                queryVesselResponse.readDashboardData &&
                typeof window !== 'undefined'
            ) {
                const vessels = queryVesselResponse.readDashboardData[0].vessels

                const vesselIDs = vessels.map((item: any) => item.id)

                let response: any = []
                if (vesselIDs.length > 0) {
                    // response = await getVessselsWithLatestStatus({
                    //     variables: {
                    //         vesselFilter: {
                    //             id: { in: vesselIDs },
                    //         },
                    //         filter: { archived: { eq: false } },
                    //     },
                    // })
                }

                const vesselsWithLatestStatus =
                    response.data?.readVessels?.nodes ?? []

                // Loop through vessels and save the tasksDue and trainingsDue properties to localStorage with this format for the keys: 'tasksDue-id' and 'trainingsDue-id'
                for (let i = 0; i < vessels.length; i++) {
                    const vessel = vessels[i]
                    localStorage.setItem(
                        `tasksDue-${vessel.id}`,
                        vessel.tasksDue,
                    )
                    localStorage.setItem(
                        `trainingsDue-${vessel.id}`,
                        vessel.trainingsDue,
                    )
                }

                const vesselsWithStatus = vessels.map(function (vessel: any) {
                    // const vesselWithStatus = vesselsWithLatestStatus.find(
                    //     (item: any) => item.id == vessel.id,
                    // )
                    // const statusHistory =
                    //     vesselWithStatus?.statusHistory.nodes ?? []
                    // const status =
                    //     statusHistory.length > 0 ? statusHistory[0] : null

                    return {
                        ...vessel,
                        status: vessel.vesselStatus.Status,
                    }
                })

                handleSetVessels(vesselsWithStatus)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })

    // const [getVessselsWithLatestStatus] = useLazyQuery(ReadVessels, {
    //     fetchPolicy: 'cache-and-network',
    //     onError: (error: any) => {
    //         console.error('getVessselsWithLatestStatus error', error)
    //     },
    // })

    const loadVessels = async () => {
        await queryVessels()
    }

    useEffect(() => {
        if (isLoading) {
            loadVessels()
            setIsLoading(false)
        }
    }, [isLoading])

    const onChangeStatusSuccess = (vessel: any, status: any) => {
        setVesselList((previousList) => {
            const dataIndex = previousList.findIndex(
                (item) => item.id == vessel.id,
            )

            if (dataIndex === -1) {
                return previousList
            }

            const newList = [...previousList]

            vessel.status = status

            newList[dataIndex] = vessel

            return newList
        })
    }

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                const vesselStatus = vessel.status
                return (
                    <>
                        {vesselStatus &&
                        vesselStatus.status !== 'OutOfService' ? (
                            <Link
                                className=" inline-flex flex-row gap-2 whitespace-nowrap items-center"
                                href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                                <div className="border-2 shrink-0 border-border rounded-full hidden small:inline-block">
                                    <VesselIcon vessel={vessel} />
                                </div>
                                <div className="grid">
                                    <span className="font-medium truncate hover:text-primary">
                                        {vessel.title}
                                    </span>

                                    {vessel.logentryID !== 0 ? (
                                        <div className="text-curious-blue-400 text-[10px]">
                                            ON VOYAGE
                                        </div>
                                    ) : (
                                        <div className="text-curious-blue-400 text-[10px]">
                                            READY FOR VOYAGE
                                        </div>
                                    )}
                                </div>
                            </Link>
                        ) : (
                            <Link
                                className="flex flex-row gap-2 whitespace-nowrap items-center"
                                href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                                <div className="relative hidden small:inline-block shrink-0 overflow-hidden border-2 border-destructive rounded-full">
                                    <VesselIcon vessel={vessel} />
                                    <div className="absolute inset-0 bg-destructive opacity-50 rounded-full">
                                        {'        '}
                                    </div>
                                </div>
                                <div className="grid">
                                    <span className="font-medium opacity-50 truncate">
                                        {vessel.title}
                                    </span>
                                    <div className="inline-block text-[10px] text-destructive">
                                        OUT OF SERVICE
                                    </div>
                                </div>
                            </Link>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'trainingsDue',
            header: 'Training',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original

                return (
                    <div className='flex flex-1 justify-center'>
                        {vessel.trainingsDue > 0 ? (
                            <div className={`alert !rounded-full flex w-8 h-8`}>
                                {vessel.trainingsDue}
                            </div>
                        ) : (
                            <div
                                className={`text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#00a396"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'tasksDue',
            header: 'Tasks',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original

                return (
                    <div className='flex flex-1 justify-center'>
                        {vessel.tasksDue > 0 ? (
                            <div className={`alert !rounded-full flex flex-shrink-0 w-8 h-8`}>
                                {vessel.tasksDue}
                            </div>
                        ) : (
                            <div
                                className={`text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            id: 'actions',
            enableHiding: false,
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <TableActionColumn
                        vessel={vessel}
                        onChangeStatusSuccess={(newStatus) =>
                            onChangeStatusSuccess(vessel, newStatus)
                        }
                    />
                )
            },
        },
    ])

    return (
        <div className="space-y-2">
            <div className="flex py-3 items-baseline gap-2 phablet:gap-4">
                <SealogsVesselsIcon />
                <Link href={`/vessel`}>
                    <H1>Vessels</H1>
                </Link>
            </div>
            <VesselStatusChart
                startMonth={subMonths(new Date(), 5)}
                endMonth={new Date()}
            />
            {filteredVesselList.length > 0 && (
                <div className="space-y-5">
                    <DataTable
                        columns={columns}
                        data={filteredVesselList.map((vessel) => ({
                            ...vessel,
                            trainingsDue: vessel.trainingsDue || 0,
                            tasksDue: vessel.tasksDue || 0,
                        }))}
                        showToolbar={false}
                        className={'p-0 border-0 shadow-none'}
                    />
                    <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center">
                        <Link
                            href={`/vessel`}
                            className="text-accent-foreground uppercase hover:text-primary text-xs">
                            See all vessels
                        </Link>
                    </div>
                </div>
            )}
        </div>
    )
}
