'use client'

import Filter from '@/components/filter'
import { GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES } from '@/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES'
import { useLazyQuery } from '@apollo/client'
import { useMemo, useState } from 'react'
import ExportButton from './export-button'
import { isOverDueTask } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import {
    Table,
    TableBody,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

import { ArrowLeft } from 'lucide-react'
import { But<PERSON>, Card, CardContent, H3, ListHeader } from '@/components/ui'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType = 'dateRange' | 'vessels' | 'category' | 'status' | 'member'

interface IFilter {
    type: FilterType
    data: any
}

const tableHeadings = [
    'Task Name',
    'Inventory',
    'Location',
    'Assigned To',
    'Status',
    'Due Date',
    'Projected',
    'Actual',
    'Difference',
]

interface IReportItem {
    taskName: string
    inventoryName?: string
    vesselName?: string
    assignedTo?: string
    status?: string
    dueDate?: Date
    dueStatus: any
    projected: number
    actual: number
    difference: number
}

export default function MaintenanceCostTrackReport() {
    const router = useRouter()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [category, setCategory] = useState<IDropdownItem | null>(null)
    const [status, setStatus] = useState<IDropdownItem | null>(null)
    const [crew, setCrew] = useState<IDropdownItem | null>(null)
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error',
                    error,
                )
            },
        },
    )

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        switch (type) {
            case 'vessels':
                setSelectedVessels(data)
                break
            case 'category':
                setCategory(data)
                break

            case 'status':
                setStatus(data)
                break

            case 'dateRange':
                setDateRange(data)
                break

            case 'member':
                setCrew(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['expires'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (selectedVessels.length > 0) {
            filter['basicComponentID'] = {
                in: selectedVessels.map((item) => item.value),
            }
        }

        if (category !== null) {
            filter['maintenanceCategoryID'] = {
                eq: category.value,
            }
        }

        if (status !== null) {
            filter['status'] = {
                eq: status.value,
            }
        }

        if (crew !== null) {
            filter['assignedToID'] = {
                eq: crew.value,
            }
        }

        getReportData({
            variables: { filter },
        })
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const body: any = reportData.map((item) => {
            return [
                item.taskName,
                item.inventoryName,
                item.vesselName,
                item.assignedTo,
                item.status,
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : '',
                item.projected.toLocaleString(),
                item.actual.toLocaleString(),
                item.difference.toLocaleString(),
            ]
        })

        const totalProjected = reportData.reduce(
            (acc, current) => acc + current.projected,
            0,
        )
        const totalActual = reportData.reduce(
            (acc, current) => acc + current.actual,
            0,
        )
        const totalDifference = reportData.reduce(
            (acc, current) => acc + current.difference,
            0,
        )

        exportPdfTable({
            body,
            headers: [
                [
                    {
                        content: 'Task Name',
                    },
                    {
                        content: 'Inventory',
                    },
                    {
                        content: 'Location',
                    },
                    {
                        content: 'Assigned To',
                    },
                    {
                        content: 'Status',
                    },
                    {
                        content: 'Due Date',
                    },
                    {
                        content: 'Projected',
                    },
                    {
                        content: 'Actual',
                    },
                    {
                        content: 'Difference',
                    },
                ],
            ],
            footers: [
                [
                    {
                        colSpan: 6,
                        content: 'Total',
                    },
                    {
                        content: totalProjected.toLocaleString(),
                    },
                    {
                        content: totalActual.toLocaleString(),
                    },
                    {
                        content: totalDifference.toLocaleString(),
                    },
                ],
            ],
            userOptions: {
                showFoot: 'lastPage',
            },
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = [
            [
                'task name',
                'inventory',
                'location',
                'assigned to',
                'status',
                'due date',
                'projected',
                'actual',
                'difference',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.taskName,
                item.inventoryName ?? 'N/A',
                item.vesselName ?? 'N/A',
                item.assignedTo ?? 'N/A',
                item.status ?? 'N/A',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : 'N/A',
                item.projected.toLocaleString(),
                item.actual.toLocaleString(),
                item.difference < 0 ? '' : item.difference.toLocaleString(),
            ])
        })

        exportCsv(csvEntries)
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readComponentMaintenanceChecks.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach((fetchedItem: any) => {
            const reportItem: IReportItem = {
                taskName: fetchedItem.name,
                vesselName: fetchedItem.basicComponent.title,
                assignedTo:
                    fetchedItem.assignedTo.id == 0
                        ? undefined
                        : `${fetchedItem.assignedTo.firstName} ${fetchedItem.assignedTo.surname}`,
                inventoryName: fetchedItem.inventory.title,
                dueDate: fetchedItem.expires
                    ? new Date(fetchedItem.expires)
                    : undefined,
                status: fetchedItem.status,
                dueStatus: isOverDueTask(fetchedItem),
                projected: fetchedItem.projected ?? 0,
                actual: fetchedItem.actual ?? 0,
                difference:
                    (fetchedItem.projected ?? 0) - (fetchedItem.actual ?? 0),
            }

            reportItems.push(reportItem)
        })

        return reportItems
    }, [called, loading, data])

    return (
        <>
            <ListHeader
                title="Maintenance cost task report"
                actions={
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter
                        onChange={handleFilterOnChange}
                        onClick={generateReport}
                    />
                    <ExportButton
                        onDownloadPdf={downloadPdf}
                        onDownloadCsv={downloadCsv}
                    />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {tableHeadings.map((heading) => (
                                    <TableHead key={heading}>
                                        {heading}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableContent
                            isLoading={called && loading}
                            reportData={reportData}
                        />
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    const totalProjected = useMemo<number>(() => {
        return reportData.reduce((acc, current) => current.projected + acc, 0)
    }, [reportData])

    const totalActual = useMemo<number>(() => {
        return reportData.reduce((acc, current) => current.actual + acc, 0)
    }, [reportData])

    if (isLoading) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center  h-32">
                        Loading...
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center  h-32">
                        No Data Available
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    return (
        <>
            <TableBody>
                {reportData.map(function (element, index) {
                    return (
                        <TableRow
                            key={`report-item-${index}`}
                            className="group border-b hover:">
                            <TableCell className="px-2 py-3 text-left w-[15%]">
                                <div className=" inline-block">
                                    {element.taskName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left  w-[15%]">
                                <div className=" inline-block">
                                    {element.inventoryName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[15%]">
                                <div className=" inline-block">
                                    {element.vesselName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[15%]">
                                <div className=" inline-block">
                                    {element.assignedTo}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block">
                                    {element.status}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block">
                                    {element.dueDate
                                        ? dayjs(element.dueDate).format(
                                              'DD/MM/YY',
                                          )
                                        : ''}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block">
                                    {element.projected.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block">
                                    {element.actual.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block">
                                    {element.difference.toLocaleString()}
                                </div>
                            </TableCell>
                        </TableRow>
                    )
                })}
            </TableBody>
            <TableFooter>
                <TableRow className={`group border-b  `}>
                    <TableHead
                        className="px-2 py-3 text-left"
                        scope="col"
                        colSpan={6}>
                        <div className="inline-block ml-3">Total</div>
                    </TableHead>
                    <TableHead className="px-2 py-3 text-left w-[10%]">
                        <div className="inline-block ">
                            {totalProjected.toLocaleString()}
                        </div>
                    </TableHead>
                    <TableHead className="px-2 py-3 text-left w-[10%]">
                        <div className="inline-block ">
                            {totalActual.toLocaleString()}
                        </div>
                    </TableHead>
                    <TableHead className="px-2 py-3 text-left w-[10%]">
                        <div className="inline-block ">
                            {(totalProjected - totalActual).toLocaleString()}
                        </div>
                    </TableHead>
                </TableRow>
            </TableFooter>
        </>
    )
}
