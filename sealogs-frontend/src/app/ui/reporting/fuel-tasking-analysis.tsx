'use client'

import Filter from '@/components/filter'
import { formatDate } from '@/app/helpers/dateHelper'
import { userHasRescueVessel } from '@/app/lib/actions'
import {
    GetTripIdsByVesselID,
    TripReport_LogBookEntrySection_Brief,
} from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import TableWrapper from '@/components/ui/table-wrapper'
import {
    Button,
    Card,
    CardContent,
    H1,
    H3,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'

const headings = [
    'Date',
    'Tasking incedent/Police number',
    'Tasking start',
    'Tasking end',
    'Fuel used CGNZ',
    'Fuel used SAROP',
]

export default function FuelTaskingAnalysis() {
    const router = useRouter()
    const [vesselID, setVesselID] = useState('')
    const [dateRange, setDateRange] = useState<any>(false)
    const [hasRescueVessel, setHasRescueVessel] = useState(false)
    const [fuelReport, setFuelReport] = useState([])

    const handleFilterChange = (filters: any) => {
        if (filters.type === 'vessel' && filters.data?.value) {
            setVesselID(filters.data?.value)
        }
        if (filters.type === 'dateRange') {
            setDateRange(filters.data)
        }
    }

    useEffect(() => {
        if (vesselID) {
            loadTripIdsByVesselID({
                variables: {
                    id: vesselID,
                },
            })
        }
    }, [vesselID])

    const [loadTripIdsByVesselID] = useLazyQuery(GetTripIdsByVesselID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            var tripIds: string[] = []
            const data = response.readOneVessel.logBookEntries.nodes?.map(
                (entry: any) =>
                    entry.logBookEntrySections.nodes?.map((section: any) => {
                        tripIds.push(section.id)
                    }),
            )
            getSectionTripReport_LogBookEntrySection({
                variables: {
                    id: tripIds,
                },
            })
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const depricatedFindCGNZConsumption = (
        cgopTasking: any,
        taskingStart: any,
    ) => {
        var fuelConsumed = taskingStart.eventType_Tasking.fuelLog.nodes.reduce(
            (acc: any, node: any) => {
                return acc + (+node.fuelBefore - +node.fuelAfter)
            },
            0,
        )
        const tasking = cgopTasking.filter(
            (event: any) =>
                event.eventType_Tasking.cgop > 0 ||
                event.eventType_Tasking.type == 'TaskingPaused' ||
                event.eventType_Tasking.type == 'TaskingResumed',
        )
        const pausedTask = tasking.filter(
            (event: any) =>
                event.eventType_Tasking.pausedTaskID ==
                    taskingStart.eventType_TaskingID &&
                event.eventType_Tasking.type == 'TaskingPaused',
        )
        const resumedTask = tasking.filter(
            (event: any) =>
                event.eventType_Tasking.openTaskID ==
                    taskingStart.eventType_TaskingID &&
                event.eventType_Tasking.type == 'TaskingResumed',
        )
        const completedTask = tasking.filter(
            (event: any) =>
                (event.eventType_Tasking.groupID ==
                    taskingStart.eventType_TaskingID ||
                    event.eventType_Tasking.completedTaskID ==
                        taskingStart.eventType_TaskingID) &&
                event.eventType_Tasking.type == 'TaskingComplete',
        )
        if (pausedTask.length > 0) {
            pausedTask.forEach((event: any) => {
                fuelConsumed -= +event.eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
            })
        }
        if (resumedTask.length > 0) {
            resumedTask.forEach((event: any) => {
                fuelConsumed += +event.eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
            })
        }
        if (completedTask.length > 0) {
            fuelConsumed -=
                +completedTask[0].eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
        } else {
            if (pausedTask.length > 0) {
                const lastPausedTask = pausedTask[pausedTask.length - 1]
                fuelConsumed -=
                    +lastPausedTask.eventType_Tasking.fuelLog.nodes.reduce(
                        (acc: any, node: any) => {
                            return acc + (+node.fuelBefore - +node.fuelAfter)
                        },
                        0,
                    )
            } else {
                fuelConsumed = 0
            }
        }
        return fuelConsumed < 0 ? 0 : fuelConsumed // Fuel consumed cannot be negative.
    }

    const depricatedFindsaropConsumption = (
        cgopTasking: any,
        taskingStart: any,
    ) => {
        var fuelConsumed = +taskingStart.eventType_Tasking.fuelLog.nodes.reduce(
            (acc: any, node: any) => {
                return acc + (+node.fuelBefore - +node.fuelAfter)
            },
            0,
        )
        const tasking = cgopTasking.filter(
            (event: any) =>
                event.eventType_Tasking.sarop > 0 ||
                event.eventType_Tasking.type == 'TaskingPaused' ||
                event.eventType_Tasking.type == 'TaskingResumed',
        )
        const pausedTask = tasking.filter(
            (event: any) =>
                event.eventType_Tasking.pausedTaskID ==
                    taskingStart.eventType_TaskingID &&
                event.eventType_Tasking.type == 'TaskingPaused',
        )
        const resumedTask = tasking.filter(
            (event: any) =>
                event.eventType_Tasking.openTaskID ==
                    taskingStart.eventType_TaskingID &&
                event.eventType_Tasking.type == 'TaskingResumed',
        )
        const completedTask = tasking.filter(
            (event: any) =>
                (event.eventType_Tasking.groupID ==
                    taskingStart.eventType_TaskingID ||
                    event.eventType_Tasking.completedTaskID ==
                        taskingStart.eventType_TaskingID) &&
                event.eventType_Tasking.type == 'TaskingComplete',
        )
        if (pausedTask.length > 0) {
            pausedTask.forEach((event: any) => {
                fuelConsumed -= +event.eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
            })
        }
        if (resumedTask.length > 0) {
            resumedTask.forEach((event: any) => {
                fuelConsumed += +event.eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
            })
        }
        if (completedTask.length > 0) {
            fuelConsumed -=
                +completedTask[0].eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
        } else {
            if (pausedTask.length > 0) {
                const lastPausedTask = pausedTask[pausedTask.length - 1]
                fuelConsumed -=
                    +lastPausedTask.eventType_Tasking.fuelLog.nodes.reduce(
                        (acc: any, node: any) => {
                            return acc + (+node.fuelBefore - +node.fuelAfter)
                        },
                        0,
                    )
            } else {
                fuelConsumed = 0
            }
        }
        return fuelConsumed < 0 ? 0 : fuelConsumed // Fuel consumed cannot be negative.
    }

    const findCGNZConsumption = (cgopTasking: any, taskingStart: any) => {
        if (taskingStart.eventType_Tasking.id < 605) {
            return depricatedFindCGNZConsumption(cgopTasking, taskingStart)
        }
        var fuelConsumed = taskingStart.eventType_Tasking.fuelLog.nodes.reduce(
            (acc: any, node: any) => {
                return acc + (+node.fuelBefore - +node.fuelAfter)
            },
            0,
        )
        const fuelLogs = cgopTasking
            .filter(
                (event: any) =>
                    event.eventCategory === 'Tasking' &&
                    event.eventType_Tasking.cgop > 0,
            )
            .map((event: any) => {
                return event.eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
            })
        fuelConsumed += fuelLogs.reduce((acc: any, node: any) => {
            return acc + node
        }, 0)

        // const tasking = cgopTasking.filter(
        //     (event: any) =>
        //         event.eventType_Tasking.cgop > 0 ||
        //         event.eventType_Tasking.type == 'TaskingPaused' ||
        //         event.eventType_Tasking.type == 'TaskingResumed',
        // )
        // const pausedTask = tasking.filter(
        //     (event: any) =>
        //         event.eventType_Tasking.pausedTaskID ==
        //             taskingStart.eventType_TaskingID &&
        //         event.eventType_Tasking.type == 'TaskingPaused',
        // )
        // const resumedTask = tasking.filter(
        //     (event: any) =>
        //         event.eventType_Tasking.openTaskID ==
        //             taskingStart.eventType_TaskingID &&
        //         event.eventType_Tasking.type == 'TaskingResumed',
        // )
        // const completedTask = tasking.filter(
        //     (event: any) =>
        //         (event.eventType_Tasking.groupID ==
        //             taskingStart.eventType_TaskingID ||
        //             event.eventType_Tasking.completedTaskID ==
        //                 taskingStart.eventType_TaskingID) &&
        //         event.eventType_Tasking.type == 'TaskingComplete',
        // )
        // if (pausedTask.length > 0) {
        //     pausedTask.forEach((event: any) => {
        //         fuelConsumed -= +event.eventType_Tasking.fuelLog.nodes.reduce(
        //             (acc: any, node: any) => {
        //                 return acc + (+node.fuelBefore - +node.fuelAfter)
        //             },
        //             0,
        //         )
        //     })
        // }
        // if (resumedTask.length > 0) {
        //     resumedTask.forEach((event: any) => {
        //         fuelConsumed += +event.eventType_Tasking.fuelLog.nodes.reduce(
        //             (acc: any, node: any) => {
        //                 return acc + (+node.fuelBefore - +node.fuelAfter)
        //             },
        //             0,
        //         )
        //     })
        // }
        // if (completedTask.length > 0) {
        //     fuelConsumed -=
        //         +completedTask[0].eventType_Tasking.fuelLog.nodes.reduce(
        //             (acc: any, node: any) => {
        //                 return acc + (+node.fuelBefore - +node.fuelAfter)
        //             },
        //             0,
        //         )
        // } else {
        //     if (pausedTask.length > 0) {
        //         const lastPausedTask = pausedTask[pausedTask.length - 1]
        //         fuelConsumed -=
        //             +lastPausedTask.eventType_Tasking.fuelLog.nodes.reduce(
        //                 (acc: any, node: any) => {
        //                     return acc + (+node.fuelBefore - +node.fuelAfter)
        //                 },
        //                 0,
        //             )
        //     } else {
        //         fuelConsumed = 0
        //     }
        // }
        return fuelConsumed < 0 ? 0 : fuelConsumed // Fuel consumed cannot be negative.
    }

    const findsaropConsumption = (cgopTasking: any, taskingStart: any) => {
        if (taskingStart.eventType_Tasking.id < 605) {
            return depricatedFindsaropConsumption(cgopTasking, taskingStart)
        }
        var fuelConsumed = +taskingStart.eventType_Tasking.fuelLog.nodes.reduce(
            (acc: any, node: any) => {
                return acc + (+node.fuelBefore - +node.fuelAfter)
            },
            0,
        )
        const fuelLogs = cgopTasking
            .filter(
                (event: any) =>
                    event.eventCategory === 'Tasking' &&
                    event.eventType_Tasking.sarop > 0,
            )
            .map((event: any) => {
                return event.eventType_Tasking.fuelLog.nodes.reduce(
                    (acc: any, node: any) => {
                        return acc + (+node.fuelBefore - +node.fuelAfter)
                    },
                    0,
                )
            })
        fuelConsumed += fuelLogs.reduce((acc: any, node: any) => {
            return acc + node
        }, 0)

        // const tasking = cgopTasking.filter(
        //     (event: any) =>
        //         event.eventType_Tasking.sarop > 0 ||
        //         event.eventType_Tasking.type == 'TaskingPaused' ||
        //         event.eventType_Tasking.type == 'TaskingResumed',
        // )
        // const pausedTask = tasking.filter(
        //     (event: any) =>
        //         event.eventType_Tasking.pausedTaskID ==
        //             taskingStart.eventType_TaskingID &&
        //         event.eventType_Tasking.type == 'TaskingPaused',
        // )
        // const resumedTask = tasking.filter(
        //     (event: any) =>
        //         event.eventType_Tasking.openTaskID ==
        //             taskingStart.eventType_TaskingID &&
        //         event.eventType_Tasking.type == 'TaskingResumed',
        // )
        // const completedTask = tasking.filter(
        //     (event: any) =>
        //         (event.eventType_Tasking.groupID ==
        //             taskingStart.eventType_TaskingID ||
        //             event.eventType_Tasking.completedTaskID ==
        //                 taskingStart.eventType_TaskingID) &&
        //         event.eventType_Tasking.type == 'TaskingComplete',
        // )
        // if (pausedTask.length > 0) {
        //     pausedTask.forEach((event: any) => {
        //         fuelConsumed -= +event.eventType_Tasking.fuelLog.nodes.reduce(
        //             (acc: any, node: any) => {
        //                 return acc + (+node.fuelBefore - +node.fuelAfter)
        //             },
        //             0,
        //         )
        //     })
        // }
        // if (resumedTask.length > 0) {
        //     resumedTask.forEach((event: any) => {
        //         fuelConsumed += +event.eventType_Tasking.fuelLog.nodes.reduce(
        //             (acc: any, node: any) => {
        //                 return acc + (+node.fuelBefore - +node.fuelAfter)
        //             },
        //             0,
        //         )
        //     })
        // }
        // if (completedTask.length > 0) {
        //     fuelConsumed -=
        //         +completedTask[0].eventType_Tasking.fuelLog.nodes.reduce(
        //             (acc: any, node: any) => {
        //                 return acc + (+node.fuelBefore - +node.fuelAfter)
        //             },
        //             0,
        //         )
        // } else {
        //     if (pausedTask.length > 0) {
        //         const lastPausedTask = pausedTask[pausedTask.length - 1]
        //         fuelConsumed -=
        //             +lastPausedTask.eventType_Tasking.fuelLog.nodes.reduce(
        //                 (acc: any, node: any) => {
        //                     return acc + (+node.fuelBefore - +node.fuelAfter)
        //                 },
        //                 0,
        //             )
        //     } else {
        //         fuelConsumed = 0
        //     }
        // }
        return fuelConsumed < 0 ? 0 : fuelConsumed // Fuel consumed cannot be negative.
    }

    const [getSectionTripReport_LogBookEntrySection] = useLazyQuery(
        TripReport_LogBookEntrySection_Brief,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                var fuelReport: any = []
                data.forEach((entry: any) => {
                    var fuelLevel = 0
                    const tasking = entry.tripEvents.nodes.filter(
                        (event: any) =>
                            event.eventCategory === 'Tasking' &&
                            event.eventType_Tasking.type !==
                                'TaskingStartUnderway',
                    )
                    const taskingStart = entry.tripEvents.nodes.filter(
                        (event: any) =>
                            event.eventCategory === 'Tasking' &&
                            event.eventType_Tasking.type ===
                                'TaskingStartUnderway',
                    )
                    taskingStart.forEach((event: any) => {
                        if (event.eventCategory === 'Tasking') {
                            // fuelLevel =
                            //     event.eventType_Tasking.fuelLevel != 0
                            //         ? event.eventType_Tasking.fuelLevel
                            //         : fuelLevel
                            // findCGNZConsumption(tasking, event)
                            fuelReport.push({
                                id: event.id,
                                fuelUsedCGNZ: findCGNZConsumption(
                                    tasking,
                                    event,
                                ),
                                fuelUsedSAROP: findsaropConsumption(
                                    tasking,
                                    event,
                                ),
                                incidentNumber:
                                    +event.eventType_Tasking?.cgop > 0
                                        ? 'CGOP: ' +
                                          event.eventType_Tasking.cgop
                                        : event.eventType_Tasking?.sarop > 0
                                          ? 'SAROP: ' +
                                            event.eventType_Tasking.sarop
                                          : '-',
                                fromLocation: entry.fromLocation,
                                toLocation: entry.toLocation,
                                arrivalTime: entry.arriveTime,
                                departureTime: entry.departTime,
                                created: entry.created,
                            })
                        }
                    })
                })
                setFuelReport(fuelReport)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const filterByDate = (entry: any) => {
        if (dateRange) {
            return (
                dayjs(entry.created).isAfter(dayjs(dateRange.startDate)) &&
                dayjs(entry.created).isBefore(dayjs(dateRange.endDate))
            )
        }
        return true
    }

    userHasRescueVessel(setHasRescueVessel)

    if (!hasRescueVessel) {
        return <div></div>
    }

    return (
        <>
            <ListHeader
                title="Fuel Tasking Analysis"
                actions={
                    <div className="flex gap-2.5">
                        <Button
                            variant={'back'}
                            onClick={() => router.push('/reporting')}>
                            Back
                        </Button>
                    </div>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter onChange={handleFilterChange} />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {headings.map((header) => (
                                    <TableHead key={header}>{header}</TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {fuelReport.length == 0 ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={headings.length}
                                        className="text-center h-32">
                                        No Data Available
                                    </TableCell>
                                </TableRow>
                            ) : (
                                fuelReport
                                    .filter((entry: any) => filterByDate(entry))
                                    .map((entry: any) => (
                                        <TableRow key={entry.id}>
                                            <TableCell>
                                                {formatDate(entry.created)}
                                            </TableCell>
                                            <TableCell>
                                                {entry.incidentNumber}
                                            </TableCell>
                                            <TableCell>
                                                {entry.fromLocation?.title
                                                    ? entry.fromLocation.title +
                                                      ' - '
                                                    : ''}
                                                {entry.departureTime}
                                            </TableCell>
                                            <TableCell>
                                                {entry.toLocation?.title
                                                    ? entry.toLocation.title +
                                                      ' - '
                                                    : ''}
                                                {entry.arrivalTime}
                                            </TableCell>
                                            <TableCell>
                                                {entry.fuelUsedCGNZ}
                                            </TableCell>
                                            <TableCell>
                                                {entry.fuelUsedSAROP}
                                            </TableCell>
                                        </TableRow>
                                    ))
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}
