'use client'

import Filter from '@/components/filter'
import { formatDate } from '@/app/helpers/dateHelper'
import { VESSEL_STATUS } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { H3, P } from '@/components/ui/typography'

import { ArrowLeft } from 'lucide-react'
import TableWrapper from '@/components/ui/table-wrapper'
import {
    Button,
    Card,
    CardContent,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'

const headings = ['Date', 'Status', 'Reason']

export default function ServiceReport() {
    const router = useRouter()
    const [vesselID, setVesselID] = useState('')
    const [dateRange, setDateRange] = useState<any>(false)
    const [vesselStatuses, setVesselStatuses] = useState([])

    const handleFilterChange = (filters: any) => {
        if (filters.type === 'vessel' && filters.data?.value) {
            setVesselID(filters.data?.value)
        }
        if (filters.type === 'dateRange') {
            setDateRange(filters.data)
        }
    }

    useEffect(() => {
        if (vesselID) {
            loadVesselStatus({
                variables: {
                    id: vesselID,
                },
            })
        }
    }, [vesselID])

    const [loadVesselStatus] = useLazyQuery(VESSEL_STATUS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readVesselStatuss.nodes
            setVesselStatuses(data)
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const filterByDate = (entry: any) => {
        if (dateRange) {
            return (
                dayjs(entry.created).isAfter(dayjs(dateRange.startDate)) &&
                dayjs(entry.created).isBefore(dayjs(dateRange.endDate))
            )
        }
        return true
    }

    const briefReport = () => {
        let lastTime = 0
        let OnVoyage = 0
        let AvailableForVoyage = 0
        let OutOfService = 0

        vesselStatuses
            ?.filter((entry: any) => filterByDate(entry))
            .forEach((entry: any) => {
                const entryTime = new Date(entry.date).getTime()

                if (lastTime > 0) {
                    const timeDiff = lastTime - entryTime

                    switch (entry.status) {
                        case 'OnVoyage':
                            OnVoyage += timeDiff
                            break
                        case 'AvailableForVoyage':
                            AvailableForVoyage += timeDiff
                            break
                        case 'OutOfService':
                            OutOfService += timeDiff
                            break
                    }
                }

                lastTime = entryTime
            })

        OnVoyage = OnVoyage > 0 ? OnVoyage / 86400000 : OnVoyage
        AvailableForVoyage =
            AvailableForVoyage > 0
                ? AvailableForVoyage / 86400000
                : AvailableForVoyage
        OutOfService = OutOfService > 0 ? OutOfService / 86400000 : OutOfService
        return (
            <div className="mt-4 space-y-2">
                <P>OnVoyage: {OnVoyage} days</P>
                <P>AvailableForVoyage: {AvailableForVoyage} days</P>
                <P>OutOfService: {OutOfService} days</P>
            </div>
        )
    }

    return (
        <>
            <ListHeader
                title="Service Report"
                actions={
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter onChange={handleFilterChange} />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {headings.map((header) => (
                                    <TableHead key={header}>{header}</TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {vesselStatuses.length == 0 ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={headings.length}
                                        className="text-center h-32">
                                        No Data Available
                                    </TableCell>
                                </TableRow>
                            ) : (
                                vesselStatuses
                                    ?.filter((entry: any) =>
                                        filterByDate(entry),
                                    )
                                    .map((entry: any) => (
                                        <TableRow key={entry.id}>
                                            <TableCell>
                                                {formatDate(entry.date)}
                                            </TableCell>
                                            <TableCell>
                                                {entry.status}
                                            </TableCell>
                                            <TableCell>
                                                {entry?.reason}
                                                {entry?.otherReason
                                                    ? ' - ' + entry?.otherReason
                                                    : ''}
                                            </TableCell>
                                            <TableCell>
                                                {entry?.expectedReturn
                                                    ? formatDate(
                                                          entry.expectedReturn,
                                                      )
                                                    : ''}
                                            </TableCell>
                                            <TableCell>
                                                {entry?.comment && (
                                                    <div
                                                        dangerouslySetInnerHTML={{
                                                            __html: entry?.comment,
                                                        }}></div>
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))
                            )}
                        </TableBody>
                    </Table>
                    {vesselStatuses.length > 0 && briefReport()}
                </CardContent>
            </Card>
        </>
    )
}
