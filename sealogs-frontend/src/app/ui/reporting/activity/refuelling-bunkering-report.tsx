'use client'

import { useCallback, useEffect, useMemo } from 'react'
import { IEventReportProps } from './main'
import ExportButton from '../export-button'
import { GET_REFUELING_BUNKERING_EVENT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

interface IReportItem {
    vesselName: string
    dateTime: Date
    location: string
    fuelTank: string
    fuelBefore: number
    fuelAdded: number
    fuelAfter: number
    comment?: string
}

const tableHeadings: string[] = [
    'Vessel',
    'Date',
    'Time',
    'Location',
    'Name of Fuel Tank',
    'Fuel before refuelling',
    'Added fuel',
    'Fuel after refuelling',
    'Comment and receipts',
]

export default function RefuellingBunkeringReport({
    dateRange,
    vessels,
    lastGenerated,
}: IEventReportProps) {
    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_REFUELING_BUNKERING_EVENT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_REFUELING_BUNKERING_EVENT_ENTRIES error',
                    error,
                )
            },
        },
    )

    const generateReport = useCallback(() => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (vessels.length > 0) {
            filter['vehicleID'] = {
                in: vessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }, [dateRange, vessels])

    useEffect(() => {
        if (lastGenerated != null) {
            generateReport()
        }
    }, [lastGenerated])

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach(function (item: any) {
            if (item.state !== 'Locked') {
                return
            }

            if (item.vehicle.id == 0) {
                return
            }

            if (item.logBookEntrySections.nodes.length === 0) {
                return
            }

            const logBookEntrySections = item.logBookEntrySections.nodes

            const tripEvents = logBookEntrySections.reduce(
                (acc: any, current: any) => [
                    ...acc,
                    ...current.tripEvents.nodes,
                ],
                [],
            )

            if (tripEvents.length === 0) {
                return
            }

            const tripEventFuelLogs = tripEvents.filter(
                (tripEvent: any) =>
                    tripEvent.eventType_RefuellingBunkering.fuelLog.nodes
                        .length > 0,
            )

            if (tripEventFuelLogs.length === 0) {
                return
            }

            const sectionMemberComments = logBookEntrySections
                .reduce((acc: any, section: any) => {
                    return [...acc, ...section.sectionMemberComments.nodes]
                }, [])
                .map((sectionComment: any) => sectionComment?.comment)
                .filter((value: any) => value != null || value != '')

            tripEventFuelLogs.forEach((tripEvent: any) => {
                const eventRefuelingBunkering =
                    tripEvent.eventType_RefuellingBunkering
                const geoLocation = eventRefuelingBunkering.geoLocation

                eventRefuelingBunkering.fuelLog.nodes.forEach(function (
                    fuelLog: any,
                ) {
                    const reportItem: IReportItem = {
                        vesselName: item.vehicle.title,
                        dateTime: new Date(fuelLog.created),
                        location: geoLocation?.title ?? 'N/A',
                        fuelBefore: fuelLog.fuelBefore,
                        fuelAdded: fuelLog.fuelAdded,
                        fuelAfter: fuelLog.fuelAfter,
                        fuelTank: fuelLog.fuelTank?.title ?? 'N/A',
                        comment: sectionMemberComments.join(', '),
                    }

                    reportItems.push(reportItem)
                })
            })
        })

        reportItems.sort((itemA, itemB) => {
            return itemA.dateTime.getTime() - itemB.dateTime.getTime()
        })

        return reportItems
    }, [called, loading, data])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries: string[][] = []

        csvEntries.push([
            'vessel',
            'date time',
            'location',
            'name of fuel tank',
            'fuel before refuelling',
            'added fuel',
            'fuel after refuelling',
            'comment and receipts',
        ])

        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                item.dateTime.toISOString(),
                item.location,
                item.fuelTank,
                item.fuelBefore + '',
                item.fuelAdded + '',
                item.fuelAfter + '',
                item.comment ?? '',
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            [
                'Vessel',
                'Date',
                'Time',
                'Location',
                'Name of Fuel Tank',
                'Fuel before refuelling',
                'Added fuel',
                'Fuel after refuelling',
                'Comment and receipts',
            ],
        ]

        const body: any = reportData.map((item) => {
            return [
                item.vesselName,
                dayjs(item.dateTime).format('DD/M/YY'),
                dayjs(item.dateTime).format('HH:mm'),
                item.location,
                item.fuelTank,
                item.fuelBefore + '',
                item.fuelAdded + '',
                item.fuelAfter + '',
                item.comment ?? '',
            ]
        })

        exportPdfTable({
            headers,
            body,
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <ExportButton
                onDownloadCsv={downloadCsv}
                onDownloadPdf={downloadPdf}
            />
            <Table>
                <TableHeader>
                    <TableRow>
                        {tableHeadings.map((heading) => (
                            <TableHead key={heading}>{heading}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableContent
                        reportData={reportData}
                        isLoading={called && loading}
                    />
                </TableBody>
            </Table>
        </div>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    No Data Available
                </TableCell>
            </TableRow>
        )
    }

    return reportData.map((element: IReportItem, index: number) => {
        return (
            <TableRow
                key={'report-item-' + index}
                className={`group border-b  hover: `}>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">{element.vesselName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {dayjs(element.dateTime).format('DD/M/YY')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {dayjs(element.dateTime).format('HH:mm')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.location}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.fuelTank}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.fuelBefore}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.fuelAdded}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block">{element.fuelAfter}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[20%]">
                    <div className=" inline-block">{element.comment}</div>
                </TableCell>
            </TableRow>
        )
    })
}
