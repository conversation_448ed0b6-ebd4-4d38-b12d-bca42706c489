'use client'

import Filter from '@/components/filter'
import { formatDate } from '@/app/helpers/dateHelper'
import {
    GetTripIdsByVesselID,
    TripReport_LogBookEntrySection_Brief,
} from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import TableWrapper from '@/components/ui/table-wrapper'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import {
    Button,
    Card,
    CardContent,
    H1,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'

const headings = ['Date', 'Fuel Tank', 'Fuel used']

export default function DetailedFuelReport() {
    const router = useRouter()
    const [vesselID, setVesselID] = useState('')
    const [dateRange, setDateRange] = useState<any>(false)
    const [fuelReport, setFuelReport] = useState([])

    const handleFilterChange = (filters: any) => {
        if (filters.type === 'vessel' && filters.data?.value) {
            setVesselID(filters.data?.value)
        }
        if (filters.type === 'dateRange') {
            setDateRange(filters.data)
        }
    }

    useEffect(() => {
        if (vesselID) {
            loadTripIdsByVesselID({
                variables: {
                    id: vesselID,
                },
            })
        }
    }, [vesselID])

    const [loadTripIdsByVesselID] = useLazyQuery(GetTripIdsByVesselID, {
        onCompleted: (response) => {
            var tripIds: string[] = []
            const data = response.readOneVessel.logBookEntries.nodes?.map(
                (entry: any) =>
                    entry.logBookEntrySections.nodes?.map((section: any) => {
                        tripIds.push(section.id)
                    }),
            )
            getTripSections({
                variables: {
                    id: tripIds,
                },
            })
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const [getTripSections] = useLazyQuery(
        TripReport_LogBookEntrySection_Brief,
        {
            onCompleted: (response: any) => {
                const data = response.readTripReport_LogBookEntrySections.nodes
                var fuelReport: any = []
                data.forEach((entry: any) => {
                    var fuelReportEvent: any = []
                    entry.tripEvents.nodes.forEach((event: any) => {
                        if (event.eventCategory === 'PassengerDropFacility') {
                            event.eventType_PassengerDropFacility.fuelLog.nodes
                                .length > 0 &&
                                fuelReportEvent.push({
                                    id: entry.id,
                                    fuelLog:
                                        event.eventType_PassengerDropFacility
                                            .fuelLog.nodes,
                                    created: entry.created,
                                    category: event.eventCategory,
                                })
                        }
                        if (event.eventCategory === 'Tasking') {
                            event.eventType_Tasking.fuelLog.nodes.length > 0 &&
                                fuelReportEvent.push({
                                    id: entry.id,
                                    fuelLog:
                                        event.eventType_Tasking.fuelLog.nodes,
                                    created: entry.created,
                                    category: event.eventType_Tasking.type,
                                })
                        }
                        if (event.eventCategory === 'RefuellingBunkering') {
                            event.eventType_RefuellingBunkering.fuelLog.nodes
                                .length > 0 &&
                                fuelReportEvent.push({
                                    id: entry.id,
                                    fuelLog:
                                        event.eventType_RefuellingBunkering
                                            .fuelLog.nodes,
                                    created: entry.created,
                                    category: event.eventCategory,
                                })
                        }
                    })
                    fuelReportEvent.length > 0 &&
                        fuelReport.push({
                            id: entry.id,
                            fuelLog: fuelReportEvent,
                            created: entry.created,
                        })
                })
                setFuelReport(fuelReport)
            },
            onError: (error: any) => {
                console.error('TripReport_LogBookEntrySection error', error)
            },
        },
    )

    const filterByDate = (entry: any) => {
        if (dateRange) {
            return (
                dayjs(entry.created).isAfter(dayjs(dateRange.startDate)) &&
                dayjs(entry.created).isBefore(dayjs(dateRange.endDate))
            )
        }
        return true
    }

    const isFuelTankDormant = (fuelTank: any) => {
        // return false // uncomment this line to display unused fuel tanks too.
        return (
            fuelTank.fuelBefore === fuelTank.fuelAfter &&
            fuelTank.fuelAdded === 0
        )
    }

    const getFuelTable = (entry: any) => {
        return (
            <>
                {entry.fuelLog
                    .filter(
                        (log: any) =>
                            log.fuelLog.filter(
                                (fuelTank: any) => !isFuelTankDormant(fuelTank),
                            ).length > 0,
                    )
                    .map((log: any, index: number) => (
                        <div key={log.id + '-' + index} className="mb-4">
                            <TableWrapper
                                headClasses="  "
                                headings={[
                                    log.category.replace(
                                        /([a-z])([A-Z])/g,
                                        '$1 $2',
                                    ) + ':left',
                                    'Start',
                                    'Added',
                                    'End',
                                ]}>
                                {log.fuelLog
                                    .filter(
                                        (fuelTank: any) =>
                                            !isFuelTankDormant(fuelTank),
                                    )
                                    .map((fuelTank: any, index: number) => (
                                        <tr
                                            key={
                                                fuelTank.fuelTank.id +
                                                '-' +
                                                log.id +
                                                '-' +
                                                index
                                            }>
                                            <td>
                                                <div className="text-left p-2">
                                                    {fuelTank.fuelTank.title}
                                                </div>
                                            </td>
                                            <td>{fuelTank.fuelBefore}</td>
                                            <td>{fuelTank.fuelAdded}</td>
                                            <td>{fuelTank.fuelAfter}</td>
                                        </tr>
                                    ))}
                            </TableWrapper>
                        </div>
                    ))}
            </>
        )
    }

    const getTotal = (entry: any) => {
        const fuelBefore = entry.fuelLog[0].fuelLog.reduce(
            (total: number, log: any) => total + log.fuelBefore,
            0,
        )
        const fuelAfter = entry.fuelLog[
            entry.fuelLog.length - 1
        ].fuelLog.reduce((total: number, log: any) => total + log.fuelAfter, 0)
        const fuelAdded = entry.fuelLog.reduce(
            (sum: number, event: any) =>
                sum +
                event.fuelLog.reduce(
                    (total: number, log: any) => total + log.fuelAdded,
                    0,
                ),
            0,
        )
        return +fuelBefore + +fuelAdded - +fuelAfter
    }

    return (
        <>
            <ListHeader
                title="Detailed Fuel Report"
                actions={
                    <Button
                        variant="back"
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter onChange={handleFilterChange} />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {headings.map((heading) => (
                                    <TableHead key={heading}>
                                        {heading}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {fuelReport.length == 0 ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={headings.length}
                                        className="text-center h-32">
                                        No Data Available
                                    </TableCell>
                                </TableRow>
                            ) : (
                                fuelReport
                                    .filter((entry: any) => filterByDate(entry))
                                    .map((entry: any) => (
                                        <TableRow key={entry.id}>
                                            <TableCell>
                                                {formatDate(entry.created)}
                                            </TableCell>
                                            <TableCell>
                                                {getFuelTable(entry)}
                                            </TableCell>
                                            <TableCell>
                                                {getTotal(entry)}
                                            </TableCell>
                                        </TableRow>
                                    ))
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}
