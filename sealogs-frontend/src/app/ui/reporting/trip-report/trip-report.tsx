'use client'

import { useLazyQuery } from '@apollo/client'
import { isEmpty } from 'lodash'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { ReadTripReport_LogBookEntrySections } from './query/readTripReport_LogBookEntrySections'
import dayjs from 'dayjs'
import Loading from '@/app/loading'
import { formatDate } from '@/app/helpers/dateHelper'
import { exportCsv } from '@/app/helpers/csvHelper'
import Filter from '@/components/filter'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'
import {
    Button,
    Card,
    CardContent,
    H1,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'

const headings = [
    'Vessel',
    'Date',
    'Depart Location',
    'Total Pax Carried',
    'Total Vehicle Carried',
    'Destination',
]

const TripReport = () => {
    const router = useRouter()
    const [filters, setFilters] = useState({} as any)
    const [tripReports, setTripReports] = useState([])

    const downloadCsv = () => {
        if (tripReports.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'Transit Trip ID',
                'Vessel',
                'Date',
                'Scheduled Depart Time',
                'Depart Time',
                'Depart Location',
                'Adult (boarded at origin)',
                'Passengers Onboard (at final destination)',
                'Total Pax Carried',
                'Trip Type',
                'Scheduled Arrive Time',
                'Arrival Time',
                'Destination',
                'Masters Remarks',
                'Stop',
                'Arrive Time',
                'Pax Off',
                'Pax On',
                'Depart Time',
            ],
        ]

        tripReports.forEach((trip: any) => {
            const baseData = [
                trip.tripReportSchedule?.transitTripID ?? '', // Transit Trip ID
                trip.logBookEntry.vehicle.title, // Vessel
                formatDate(trip.logBookEntry.startDate, false), // Date
                trip.tripScheduleDepartTime ?? '', // Scheduled Depart Time
                trip.departTime ?? '', // Depart Time
                trip.fromLocation.title ?? '', // Depart Location
                trip.pob > 0 ? trip.pob.toString() : '', // Adult (boarded at origin)
            ]

            if (trip.tripReport_Stops?.nodes?.length > 0) {
                trip.tripReport_Stops.nodes.forEach((stop: any) => {
                    const rowData = [...baseData]
                    rowData.push(
                        trip.pob - stop.paxDeparted + stop.paxJoined > 0
                            ? (
                                  trip.pob -
                                  stop.paxDeparted +
                                  stop.paxJoined
                              ).toString()
                            : '', // Passengers On Board (at final destination)
                        trip.pob + stop.paxJoined > 0
                            ? (trip.pob + stop.paxJoined).toString()
                            : '', // Total Pax Carried
                        trip.tripReportScheduleID > 0
                            ? 'Scheduled'
                            : 'Unscheduled', // Trip Type
                        trip.tripScheduleArriveTime ?? '', // Scheduled Arrive Time
                        trip.arriveTime ?? '', // Arrival Time
                        trip.toLocation?.title ?? '', // Destination
                        trip.comment ?? '', // Masters Remarks
                        stop.stopLocation?.title ?? '', // Stop
                        stop.arriveTime ?? '', // Arrive Time
                        stop.paxDeparted?.toString() ?? '', // Pax Off
                        stop.paxJoined?.toString() ?? '', // Pax On
                        stop.departTime ?? '', // Depart Time
                    )
                    csvEntries.push(rowData)
                })
            } else {
                const rowData = [...baseData]
                rowData.push(
                    trip.pob > 0 ? trip.pob.toString() : '', // Passengers On Board (at final destination)
                    trip.pob > 0 ? trip.pob.toString() : '', // Total Pax Carried
                    trip.tripReportScheduleID > 0 ? 'Scheduled' : 'Unscheduled', // Trip Type
                    trip.tripScheduleArriveTime ?? '', // Scheduled Arrive Time
                    trip.arriveTime ?? '', // Arrival Time
                    trip.toLocation?.title ?? '', // Destination
                    trip.comment ?? '', // Masters Remarks
                    '',
                    '',
                    '',
                    '',
                    '', // Empty values for stop columns
                )
                csvEntries.push(rowData)
            }
        })

        const csvFilename = `trip-report-${dayjs().format('YYYY-MM-DD-HHmmss')}.csv`
        exportCsv(csvEntries, csvFilename)
    }

    const handleFilterChange = (item: any) => {
        setFilters({ ...filters, [item.type]: item.data })
    }

    const [
        readTripReport_LogBookEntrySections,
        { loading: readTripReport_LogBookEntrySectionsLoading },
    ] = useLazyQuery(ReadTripReport_LogBookEntrySections, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripReport_LogBookEntrySections.nodes
            setTripReports(data)
        },
        onError: (error: any) => {
            console.error('readTripReport_LogBookEntrySections error', error)
        },
    })

    const loadTripReport = async () => {
        // vesselIds: filters.vessels?.map((vessel: any) => vessel.value),

        // departTimeFrom: filters.fromTime,
        // departTimeTo: filters.toTime,
        // limit: 100,
        // offset: 0,
        const filter: any = {
            archived: { eq: false },
        }
        // Process date range
        const fromDate = filters.dateRange?.startDate
            ? dayjs(filters.dateRange.startDate).format('YYYY-MM-DD')
            : null
        const toDate = filters.dateRange?.endDate
            ? dayjs(filters.dateRange.endDate).format('YYYY-MM-DD')
            : null

        if (fromDate && toDate) {
            filter.logBookEntry = {
                ...filter.logBookEntry,
                startDate: {
                    gte: fromDate,
                    lte: toDate,
                },
            }
        }
        // Process locations
        const fromLocationId = filters.fromLocation?.value
        const toLocationId = filters.toLocation?.value
        if (fromLocationId) {
            filter.fromLocationID = { eq: fromLocationId }
        }
        if (toLocationId) {
            filter.toLocationID = { eq: toLocationId }
        }
        // Process departure time
        const fromTime = filters.fromTime
        const toTime = filters.toTime
        if (fromTime && toTime) {
            filter.departTime = {
                gte: fromTime,
                lte: toTime,
            }
        } else if (fromTime) {
            filter.departTime = {
                gte: fromTime,
            }
        } else if (toTime) {
            filter.departTime = {
                lte: toTime,
            }
        }
        // Add numberPax filter if noPax is true
        if (filters.noPax) {
            filter.numberPax = { eq: 0 }
        }
        // Process vessels
        if (filters.vessels?.length > 0) {
            filter.logBookEntry = {
                ...filter.logBookEntry,
                vehicleID: {
                    in: filters.vessels.map((vessel: any) => vessel.value),
                },
            }
        }
        await readTripReport_LogBookEntrySections({
            variables: {
                filter: filter,
                limit: 100,
                offset: 0,
            },
        })
    }

    useEffect(() => {
        if (!isEmpty(filters)) loadTripReport()
    }, [filters])

    return (
        <>
            <ListHeader
                title="Trip Report"
                actions={
                    <div className="flex">
                        <Button
                            variant="back"
                            onClick={() => router.push('/reporting')}>
                            Back
                        </Button>
                    </div>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter
                        tripReportFilterData={filters}
                        onChange={handleFilterChange}
                    />
                    <div className="flex justify-end gap-3">
                        <SeaLogsButton
                            text="Download CSV"
                            type="primary"
                            action={downloadCsv}
                        />
                    </div>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {headings.map((heading) => (
                                    <TableHead key={heading}>
                                        {heading}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableContent
                            isLoading={
                                readTripReport_LogBookEntrySectionsLoading
                            }
                            data={tripReports}
                        />
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}

const TableContent = ({
    isLoading,
    data,
}: {
    isLoading: boolean
    data: Array<any>
}) => {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={headings.length}
                    className="text-center h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (data.length === 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={headings.length}
                    className="text-center h-32">
                    No data available
                </TableCell>
            </TableRow>
        )
    }

    return data.map((trip) => (
        <TableRow key={trip.id}>
            <TableCell>{trip.logBookEntry.vehicle.title}</TableCell>
            <TableCell>{formatDate(trip.logBookEntry.startDate)}</TableCell>
            <TableCell>{trip.fromLocation.title}</TableCell>
            <TableCell>
                {trip.tripReport_Stops?.nodes?.length > 0
                    ? trip.pob +
                      trip.tripReport_Stops.nodes.reduce(
                          (total: number, stop: any) =>
                              total + (stop.paxJoined || 0),
                          0,
                      )
                    : trip.pob}
            </TableCell>
            <TableCell>{trip.vob > 0 && trip.vob}</TableCell>
            <TableCell>{trip.toLocation.title}</TableCell>
        </TableRow>
    ))
}

export default TripReport
