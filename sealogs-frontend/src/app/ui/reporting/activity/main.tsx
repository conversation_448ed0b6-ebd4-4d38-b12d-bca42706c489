import Filter from '@/components/filter'
import React, { useState } from 'react'
import ArrivalDepartureReport from './arrival-departure-report'
import Refuelling<PERSON>unkering<PERSON>eport from './refuelling-bunkering-report'
import InfringementNoticesReport from './infringement-notices-report'
import RestrictedVisibilityEventReport from './restricted-visibility-event-report'
import BarCrossingEventReport from './bar-crossing-event-report'
import { useRouter } from 'next/navigation'

import { ArrowLeft } from 'lucide-react'
import { Combobox } from '@/components/ui/comboBox'
import {
    Button,
    Card,
    CardContent,
    CardHeader,
    H1,
    ListHeader,
} from '@/components/ui'

export interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

export interface IDropdownItem {
    label: string
    value: string
}

export interface IEventReportProps {
    vessels: IDropdownItem[]
    dateRange: DateRange
    lastGenerated: String | null
}

export const activityReportTypes: IDropdownItem[] = [
    { label: 'Arrival/Departure', value: 'arrival_departure' },
    { label: 'Refueling/Bunkering', value: 'refueling_bunkering' },
    { label: 'Infringement Notices', value: 'infringement_notices' },
    { label: 'Restricted Visibility Event', value: 'restricted_visibility' },
    { label: 'Bar Crossing Event', value: 'bar_crossing' },
]

export default function MainActivityReport() {
    const router = useRouter()
    const [selectedReportType, setSelectedReportType] =
        useState<IDropdownItem>()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })
    const [lastGenerated, setLastGenerated] = useState<String | null>(null)

    const handleFilterOnChange = ({
        type,
        data,
    }: {
        type: 'dateRange' | 'report_type' | 'vessels'
        data: any
    }) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'report_type':
                setSelectedReportType(data)
                setLastGenerated(null)
                break

            case 'vessels':
                setSelectedVessels(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        setLastGenerated(new Date().toISOString())
    }

    return (
        <>
            <ListHeader
                title="Activity Reports"
                actions={
                    <div className="flex">
                        <Button
                            variant="back"
                            onClick={() => router.push('/reporting')}>
                            Back
                        </Button>
                    </div>
                }
            />
            <Card className="mt-8">
                <CardContent>
                    <div className="flex flex-col md:flex-row md:justify-between gap-4 mb-8">
                        <div>
                            <Combobox
                                options={activityReportTypes}
                                value={selectedReportType}
                                onChange={(selectedOption: any) => {
                                    setSelectedReportType(selectedOption)
                                }}
                                title="Activity Type"
                                placeholder="Activity Type"
                                multi={false}
                            />
                        </div>
                        <Filter
                            onChange={handleFilterOnChange}
                            onClick={generateReport}
                        />
                    </div>

                    {selectedReportType?.value === 'arrival_departure' && (
                        <ArrivalDepartureReport
                            vessels={selectedVessels}
                            dateRange={dateRange}
                            lastGenerated={lastGenerated}
                        />
                    )}
                    {selectedReportType?.value === 'refueling_bunkering' && (
                        <RefuellingBunkeringReport
                            vessels={selectedVessels}
                            dateRange={dateRange}
                            lastGenerated={lastGenerated}
                        />
                    )}
                    {selectedReportType?.value === 'infringement_notices' && (
                        <InfringementNoticesReport
                            vessels={selectedVessels}
                            dateRange={dateRange}
                            lastGenerated={lastGenerated}
                        />
                    )}
                    {selectedReportType?.value === 'restricted_visibility' && (
                        <RestrictedVisibilityEventReport
                            vessels={selectedVessels}
                            dateRange={dateRange}
                            lastGenerated={lastGenerated}
                        />
                    )}
                    {selectedReportType?.value === 'bar_crossing' && (
                        <BarCrossingEventReport
                            vessels={selectedVessels}
                            dateRange={dateRange}
                            lastGenerated={lastGenerated}
                        />
                    )}
                </CardContent>
            </Card>
        </>
    )
}
