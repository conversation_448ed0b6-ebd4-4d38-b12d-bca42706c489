'use client'

import ExportButton from '../export-button'
import { IEventReportProps } from './main'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { GET_ARRIVAL_DEPARTURE_EVENT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

const tableHeadings: string[] = [
    'Vessel',
    'Date',
    'Time',
    'Title',
    'Location',
    'Passengers On',
    'Passengers Off',
]

interface IReportItem {
    vesselID: number
    vesselName: string
    date: Date
    time?: String
    title: String
    location: String
    passengersBoarding: number
    passengersEmbarking: number
}

export default function ArrivalDepartureReport({
    dateRange,
    vessels,
    lastGenerated,
}: IEventReportProps) {
    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_ARRIVAL_DEPARTURE_EVENT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_ARRIVAL_DEPARTURE_EVENT_ENTRIES error',
                    error,
                )
            },
        },
    )

    const generateReport = useCallback(() => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (vessels.length > 0) {
            filter['vehicleID'] = {
                in: vessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }, [dateRange, vessels])

    useEffect(() => {
        if (lastGenerated != null) {
            generateReport()
        }
    }, [lastGenerated])

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach(function (item: any) {
            if (item.state !== 'Locked') {
                return
            }

            if (item.vehicle.id == 0) {
                return
            }

            if (item.logBookEntrySections.nodes.length === 0) {
                return
            }

            const logBookEntrySections = item.logBookEntrySections.nodes

            const tripEvents = logBookEntrySections.reduce(
                (acc: any, current: any) => [
                    ...acc,
                    ...current.tripEvents.nodes,
                ],
                [],
            )

            if (tripEvents.length === 0) {
                return
            }

            tripEvents.forEach((tripEvent: any) => {
                const event = tripEvent.eventType_PassengerDropFacility

                const reportItem: IReportItem = {
                    vesselID: item.vehicle.id,
                    vesselName: item.vehicle.title,
                    date: event.created
                        ? new Date(event.created)
                        : new Date(item.created),
                    title: event.title,
                    location: event.geoLocation?.title ?? 'N/A',
                    passengersBoarding: event.paxOn ?? 0,
                    passengersEmbarking: event.paxOff ?? 0,
                    time: event.time,
                }

                reportItems.push(reportItem)
            })
        })

        reportItems.sort((itemA, itemB) => {
            return itemA.date.getTime() - itemB.date.getTime()
        })

        return reportItems
    }, [called, loading, data])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = []

        csvEntries.push([
            'vessel',
            'date',
            'time',
            'title',
            'location',
            'passengers on',
            'passengers off',
        ])

        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                dayjs(item.date).format('DD/MM/YYYY'),
                item.time ?? dayjs(item.date).format('HH:mm'),
                item.title,
                item.location,
                item.passengersBoarding,
                item.passengersEmbarking,
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            [
                'Vessel',
                'Date',
                'Time',
                'Title',
                'Location',
                'Passengers on',
                'Passengers off',
            ],
        ]

        const body: any = reportData.map((item) => {
            return [
                item.vesselName,
                dayjs(item.date).format('DD/MM/YYYY'),
                item.time ?? dayjs(item.date).format('HH:mm'),
                item.title,
                item.location,
                item.passengersBoarding,
                item.passengersEmbarking,
            ]
        })

        exportPdfTable({
            headers,
            body,
        })
    }

    return (
        <div className="flex flex-col gap-4">
            <ExportButton
                onDownloadCsv={downloadCsv}
                onDownloadPdf={downloadPdf}
            />
            <Table>
                <TableHeader>
                    <TableRow>
                        {tableHeadings.map((heading) => (
                            <TableHead key={heading}>{heading}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableContent
                        isLoading={called && loading}
                        reportData={reportData}
                    />
                </TableBody>
            </Table>
        </div>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    No Data Available
                </TableCell>
            </TableRow>
        )
    }

    return reportData.map((element: IReportItem, index: number) => {
        return (
            <TableRow
                key={'report-item-' + index}
                className={`group border-b  hover: `}>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">{element.vesselName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {dayjs(element.date).format('DD/M/YY')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[10%]">
                    <div className=" inline-block ">
                        {element.time ?? dayjs(element.date).format('HH:mm')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">{element.title}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">{element.location}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">
                        {element.passengersBoarding}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left w-[15%]">
                    <div className=" inline-block">
                        {element.passengersEmbarking}
                    </div>
                </TableCell>
            </TableRow>
        )
    })
}
