import gql from 'graphql-tag'

export const ReadSeaLogsMembers = gql`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: <PERSON><PERSON> }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`
