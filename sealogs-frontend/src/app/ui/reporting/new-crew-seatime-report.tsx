'use client'

import Filter from '@/components/filter'
import { CrewMembers_LogBookEntrySection } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { useCallback, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import ExportButton from './export-button'
import { useRouter } from 'next/navigation'
import {
    Table,
    TableBody,
    TableHeader,
    TableCell,
    TableHead,
    TableRow,
} from '@/components/ui/table'

import { Button, Card, CardContent, ListHeader } from '@/components/ui'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

interface IReportItem {
    crewID: number
    crewName: string
    vesselID: number
    vesselName: string
    loginTime: Date
    logoutTime: Date
    totalLoggedMinutes: number
    loggedDuration: {
        hours: number
        minutes: number
    }
    dutyPerformedID: number
    primaryDuty: string
    workDetails?: string
}

export default function NewCrewSeatimeReport() {
    const router = useRouter()
    const [selectedCrews, setSelectedCrews] = useState<IDropdownItem[]>([])
    const [selectedDuties, setSelectedDuties] = useState<IDropdownItem[]>([])
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [reportMode, setReportMode] = useState<'detailed' | 'summary'>(
        'detailed',
    )
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const handleFilterOnChange = ({
        type,
        data,
    }: {
        type: 'dateRange' | 'members' | 'vessels' | 'crewDuty' | 'reportMode'
        data: any
    }) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'members':
                setSelectedCrews(data)
                break

            case 'vessels':
                setSelectedVessels(data)
                break

            case 'crewDuty':
                setSelectedDuties(data)
                break

            case 'reportMode':
                setReportMode(data)
                break

            default:
                break
        }
    }

    const [getReportData, { called, loading, data }] = useLazyQuery(
        CrewMembers_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryLogBookEntrySections error', error)
            },
        },
    )

    const generateReport = useCallback(() => {
        const filter: any = {}

        const logBookFilter: any = {
            vehicleID: null,
            startDate: null,
        }

        if (selectedCrews.length > 0) {
            filter['crewMemberID'] = {
                in: selectedCrews.map((crew) => crew.value),
            }
        }

        if (selectedDuties.length > 0) {
            filter['dutyPerformedID'] = {
                in: selectedDuties.map((duty) => duty.value),
            }
        }

        if (selectedVessels.length > 0) {
            logBookFilter.vehicleID = {
                in: selectedVessels.map((vessel) => vessel.value),
            }
        }

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            logBookFilter.startDate = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (
            logBookFilter.vehicleID !== null ||
            logBookFilter.startDate !== null
        ) {
            if (logBookFilter.vehicleID === null) {
                delete logBookFilter.vehicleID
            }

            if (logBookFilter.startDate === null) {
                delete logBookFilter.startDate
            }

            filter['logBookEntry'] = logBookFilter
        }

        getReportData({
            variables: {
                filter,
            },
        })
    }, [selectedCrews, selectedVessels, dateRange, getReportData])

    const reportData = useMemo<IReportItem[]>(() => {
        const reportData =
            data?.readCrewMembers_LogBookEntrySections?.nodes ?? []

        const filteredData = reportData.filter(
            (item: any) => item.punchOut !== null,
        )

        const reportItems: IReportItem[] = filteredData.map((item: any) => {
            const loggedInTime = dayjs(item.punchIn)
            const loggedOutTime = dayjs(item.punchOut)

            const loggedDurationMinutes = loggedOutTime.diff(
                loggedInTime,
                'minutes',
            )

            const hours =
                loggedDurationMinutes >= 60
                    ? Math.floor(loggedDurationMinutes / 60)
                    : 0
            const minutes = loggedDurationMinutes % 60

            const reportItem: IReportItem = {
                crewID: +item.crewMember.id,
                crewName: `${item.crewMember.firstName} ${item.crewMember.surname}`,
                totalLoggedMinutes: loggedDurationMinutes,
                loggedDuration: { hours, minutes },
                loginTime: new Date(item.punchIn),
                logoutTime: new Date(item.punchOut),
                dutyPerformedID: +item.dutyPerformedID,
                primaryDuty: item.dutyPerformed.title,
                vesselID: +item.logBookEntry.vehicleID,
                vesselName: item.logBookEntry.vehicle.title,
                workDetails: item.workDetails,
            }

            return reportItem
        })

        if (reportMode === 'detailed') {
            return reportItems
        }

        //create combined id string from crewID, dutyID, vesselID
        const combinedIDs = reportItems.map(
            (item) => `${item.crewID}|${item.dutyPerformedID}|${item.vesselID}`,
        )

        const summarizedReportItems: IReportItem[] = []

        new Set(combinedIDs).forEach((value) => {
            const [crewID, dutyPerformedID, vesselID] = value.split('|')

            const relatedReportItems = reportItems.filter((value) => {
                return (
                    value.crewID === +crewID &&
                    value.dutyPerformedID === +dutyPerformedID &&
                    value.vesselID === +vesselID
                )
            })

            const totalLoggedMinutes = relatedReportItems.reduce(
                (prev, current) => prev + current.totalLoggedMinutes,
                0,
            )

            const singleRelatedReportItem = relatedReportItems[0]

            const hours =
                totalLoggedMinutes >= 60
                    ? Math.floor(totalLoggedMinutes / 60)
                    : 0
            const minutes = totalLoggedMinutes % 60

            const item: IReportItem = {
                ...singleRelatedReportItem,
                loginTime: dateRange.startDate!,
                logoutTime: dateRange.endDate!,
                totalLoggedMinutes,
                loggedDuration: {
                    hours,
                    minutes,
                },
            }

            summarizedReportItems.push(item)
        })

        return summarizedReportItems
    }, [called, data, loading, reportMode])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = []

        csvEntries.push([
            'crew',
            'vessel',
            'duty',
            'signed in',
            'signed out',
            'time spent',
        ])
        reportData.forEach((item) => {
            csvEntries.push([
                item.crewName,
                item.vesselName,
                item.primaryDuty,
                item.loginTime.toISOString(),
                item.logoutTime.toISOString(),
                `${item.loggedDuration.hours > 0 ? `${item.loggedDuration.hours}h ` : ''}${item.loggedDuration.minutes}m`,
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            ['Crew', 'Vessel', 'Duty', 'Signed in', 'Signed out', 'Time spent'],
        ]

        const data: any = reportData.map(function (item) {
            return [
                item.crewName + '',
                item.vesselName + '',
                item.primaryDuty + '',
                dayjs(item.loginTime).format('DD/MM/YY HH:mm') + '',
                dayjs(item.logoutTime).format('DD/MM/YY HH:mm') + '',
                `${item.loggedDuration.hours > 0 ? `${item.loggedDuration.hours}h ` : ''}${item.loggedDuration.minutes}m`,
            ]
        })

        exportPdfTable({
            headers,
            body: data,
        })
    }

    return (
        <>
            <ListHeader
                title="Crew Seatime Report"
                actions={
                    <div className="flex">
                        <Button
                            variant="back"
                            onClick={() => router.push('/reporting')}>
                            Back
                        </Button>
                    </div>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter
                        onChange={handleFilterOnChange}
                        onClick={generateReport}
                    />
                    <ExportButton
                        onDownloadPdf={downloadPdf}
                        onDownloadCsv={downloadCsv}
                    />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Crew member name</TableHead>
                                <TableHead>Vessel</TableHead>
                                <TableHead>Duty</TableHead>
                                <TableHead>Signed in</TableHead>
                                <TableHead>Signed out</TableHead>
                                <TableHead>Time spent</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <TableContent
                                isLoading={called && loading}
                                reportData={reportData}
                            />
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableRow>
                <TableCell colSpan={6} className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell colSpan={6} className="text-center  h-32">
                    No Data Found
                </TableCell>
            </TableRow>
        )
    }

    return reportData.map((element: IReportItem, index: number) => {
        return (
            <TableRow
                key={`${element.crewID}-${index}`}
                className={`group border-b  hover: `}>
                <TableCell className="px-2 py-3 text-left">
                    <div className=" inline-block ml-3">{element.crewName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left">
                    <div className=" inline-block ">{element.vesselName}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left">
                    <div className=" inline-block ">{element.primaryDuty}</div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left">
                    <div className=" inline-block ">
                        {dayjs(element.loginTime).format('DD/M/YY HH:mm')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left">
                    <div className=" inline-block ">
                        {dayjs(element.logoutTime).format('DD/M/YY HH:mm')}
                    </div>
                </TableCell>
                <TableCell className="px-2 py-3 text-left">
                    <div className=" inline-block ">
                        {element.loggedDuration.hours != 0
                            ? `${element.loggedDuration.hours}h, `
                            : ''}
                        {element.loggedDuration.minutes}m
                    </div>
                </TableCell>
            </TableRow>
        )
    })
}
