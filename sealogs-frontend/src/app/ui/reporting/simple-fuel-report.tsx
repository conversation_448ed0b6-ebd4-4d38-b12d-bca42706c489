'use client'

import Filter from '@/components/filter'
import { useMemo, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import { GET_SIMPLE_FUEL_REPORT_ENTRIES } from '@/app/lib/graphQL/query/reporting'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import ExportButton from './export-button'
import { useRouter } from 'next/navigation'
import {
    Table,
    TableBody,
    TableCell,
    TableFooter,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { H3 } from '@/components/ui/typography'
import { Card, CardContent, CardHeader, ListHeader } from '@/components/ui'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

interface IReportItem {
    logBookEntryID: number
    vesselID: number
    vesselName: string
    logbookDate: Date
    fuelTankID: number
    fuelTankName: string
    fuelStart: number
    fuelAdded: number
    fuelEnd: number
    fuelUsed: number
    comments?: string
}

const tableHeadings = [
    'Vessel',
    'Log Entry',
    'Fuel Tank',
    'Fuel Start',
    'Fuel Added',
    'Fuel End',
    'Fuel Used',
    'Comments',
]

export default function SimpleFuelReport() {
    const router = useRouter()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const handleFilterOnChange = ({
        type,
        data,
    }: {
        type: 'dateRange' | 'vessels'
        data: any
    }) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'vessels':
                setSelectedVessels(data)
                break

            default:
                break
        }
    }

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_SIMPLE_FUEL_REPORT_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryLogBookEntrySections error', error)
            },
        },
    )

    const generateReport = () => {
        const filter: any = {}

        if (dateRange.startDate !== null && dateRange.endDate !== null) {
            filter['startDate'] = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        if (selectedVessels.length > 0) {
            filter['vehicleID'] = {
                in: selectedVessels.map((v) => v.value),
            }
        }

        getReportData({
            variables: { filter },
        })
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData: any = data?.readLogBookEntries?.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const filteredItems = fetchedData.filter(function (item: any) {
            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== '0'
        })

        if (filteredItems.length === 0) {
            return []
        }

        const items: IReportItem[] = []

        filteredItems.forEach((item: any) => {
            if (item.state !== 'Locked') {
                return
            }

            const logBookDate = new Date(item.startDate)
            const vessel = item.vehicle

            const fuelLogs = item.fuelLog.nodes.filter(
                (item: any) => item.fuelTank.id != 0,
            )

            const fuelTanks = fuelLogs.reduce((acc: any, log: any) => {
                return { ...acc, [log.fuelTank.id]: log.fuelTank.title }
            }, {})

            const logBookEntrySections = item.logBookEntrySections.nodes
            const tripEvents = logBookEntrySections.reduce(
                (acc: any, section: any) => {
                    return [...acc, ...section.tripEvents.nodes]
                },
                [],
            )
            const sectionMemberComments = logBookEntrySections
                .reduce((acc: any, section: any) => {
                    return [...acc, ...section.sectionMemberComments.nodes]
                }, [])
                .map((sectionComment: any) => sectionComment?.comment)
                .filter((value: any) => value != null || value != '')

            for (const key in fuelTanks) {
                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {
                    const fuelTankName = fuelTanks[key]

                    const fuelTankLogs = fuelLogs.filter(
                        (item: any) => item.fuelTankID == key,
                    )

                    const fuelLogStart = fuelTankLogs[0]
                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1]

                    const fuelStart = fuelLogStart?.fuelBefore ?? 0
                    const fuelAdded = calculateFuelAddedFromTripEvents(
                        tripEvents,
                        key,
                    )
                    const fuelEnd = fuelLogEnd?.fuelAfter ?? 0
                    const fuelUsed = fuelStart + fuelAdded - fuelEnd

                    const reportItem: IReportItem = {
                        logBookEntryID: item.id,
                        vesselID: vessel.id,
                        logbookDate: logBookDate,
                        vesselName: vessel.title,
                        fuelTankID: Number(key),
                        fuelTankName: fuelTankName,
                        fuelStart,
                        fuelAdded,
                        fuelEnd,
                        fuelUsed,
                        comments: sectionMemberComments.join(', '),
                    }
                    items.push(reportItem)
                }
            }
        })

        return items
    }, [data, called, loading])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = []

        csvEntries.push([
            'vessel',
            'log entry',
            'fuel tank',
            'fuel start',
            'fuel added',
            'fuel end',
            'fuel used',
            'comments',
        ])
        reportData.forEach((item) => {
            csvEntries.push([
                item.vesselName,
                item.logbookDate.toISOString(),
                item.fuelTankName,
                item.fuelStart,
                item.fuelAdded,
                item.fuelEnd,
                item.fuelUsed,
                item.comments ?? '',
            ])
        })

        exportCsv(csvEntries)
    }
    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const data: any = reportData.map(function (item) {
            return [
                item.vesselName + '',
                dayjs(item.logbookDate).format('DD/MM/YY') + '',
                item.fuelTankName + '',
                item.fuelStart.toLocaleString(),
                item.fuelAdded.toLocaleString(),
                item.fuelEnd.toLocaleString(),
                item.fuelUsed.toLocaleString(),
                `${item.comments ?? ''} `,
            ]
        })

        const totalUsedFuel = reportData.reduce<number>(
            (acc, current) => acc + current.fuelUsed,
            0,
        )

        exportPdfTable({
            headers: [
                [
                    { content: 'Vessel' },
                    { content: 'Log Entry' },
                    { content: 'Fuel Tank' },
                    { content: 'Fuel Start' },
                    { content: 'Fuel Added' },
                    { content: 'Fuel End' },
                    { content: 'Fuel Used' },
                    {
                        content: 'Comments',
                        styles: {
                            cellWidth: 60,
                        },
                    },
                ],
            ],
            body: data,
            footers: [
                [
                    {
                        colSpan: 6,
                        content: 'Total Fuel Used',
                    },
                    {
                        content: totalUsedFuel.toLocaleString(),
                    },
                    {
                        content: '',
                    },
                ],
            ],
            userOptions: {
                showFoot: 'lastPage',
            },
        })
    }

    return (
        <>
            <ListHeader
                title="Simple Fuel Report"
                actions={
                    <div className="flex">
                        <Button
                            variant="back"
                            onClick={() => router.push('/reporting')}>
                            Back
                        </Button>
                    </div>
                }
            />
            <Card className="mt-8">
                <CardHeader className="gap-4">
                    <Filter
                        onChange={handleFilterOnChange}
                        onClick={generateReport}
                    />
                    <ExportButton
                        onDownloadPdf={downloadPdf}
                        onDownloadCsv={downloadCsv}
                    />
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {tableHeadings.map((heading) => (
                                    <TableHead key={heading}>
                                        {heading}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>

                        <TableContent
                            isLoading={called && loading}
                            reportData={reportData}
                        />
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    const totalFuelUsed = useMemo<number>(() => {
        return reportData.reduce<number>(
            (acc, current) => current.fuelUsed + acc,
            0,
        )
    }, [reportData])

    if (isLoading) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    Loading...
                </TableCell>
            </TableRow>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableRow>
                <TableCell
                    colSpan={tableHeadings.length}
                    className="text-center  h-32">
                    No Data Available
                </TableCell>
            </TableRow>
        )
    }

    return (
        <>
            <TableBody>
                {reportData.map((element: IReportItem, index: number) => {
                    return (
                        <TableRow
                            key={`${element.logBookEntryID}-${element.fuelTankID}-${element.vesselID}`}
                            className={`group border-b  hover: `}>
                            <TableCell className="px-2 py-3 text-left w-[15%]">
                                <div className=" inline-block ml-3">
                                    {element.vesselName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block ">
                                    {dayjs(element.logbookDate).format(
                                        'DD/M/YY',
                                    )}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block ">
                                    {element.fuelTankName}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block ">
                                    {element.fuelStart.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block ">
                                    {element.fuelAdded.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block ">
                                    {element.fuelEnd.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[10%]">
                                <div className=" inline-block ">
                                    {element.fuelUsed.toLocaleString()}
                                </div>
                            </TableCell>
                            <TableCell className="px-2 py-3 text-left w-[25%]">
                                <div className=" inline-block ">
                                    {element.comments}
                                </div>
                            </TableCell>
                        </TableRow>
                    )
                })}
            </TableBody>
            <TableFooter>
                <TableRow className={`group border-b`}>
                    <TableCell
                        className="px-2 py-3 text-left"
                        scope="col"
                        colSpan={6}>
                        <div className="inline-block ml-3">Total Fuel Used</div>
                    </TableCell>
                    <TableCell className="px-2 py-3 text-left w-[10%]">
                        <div className="inline-block ">
                            {totalFuelUsed.toLocaleString()}
                        </div>
                    </TableCell>
                    <TableCell className="px-2 py-3 text-left w-[25%]"></TableCell>
                </TableRow>
            </TableFooter>
        </>
    )
}

function getFuelAddedByFuelTankID(fuelLogs: any, fuelTankID: any) {
    if (fuelLogs.length === 0) {
        return 0
    }

    const fuelTankLogs = fuelLogs.filter(
        (log: any) => log.fuelTankID == fuelTankID,
    )

    return fuelTankLogs.reduce(
        (acc: number, log: any) => acc + log.fuelAdded,
        0,
    )
}

function calculateFuelAddedFromTripEvents(
    tripEvents: any,
    fuelTankID: any,
): number {
    const fuelAddedLogs: number[] = tripEvents.map(function (tripEvent: any) {
        if (tripEvent.eventCategory === 'RefuellingBunkering') {
            const fuelLogs =
                tripEvent.eventType_RefuellingBunkering.fuelLog.nodes

            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID)
        }

        if (tripEvent.eventCategory === 'Tasking') {
            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes

            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID)
        }

        if (tripEvent.eventCategory === 'PassengerDropFacility') {
            const fuelLogs =
                tripEvent.eventType_PassengerDropFacility.fuelLog.nodes

            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID)
        }

        return 0
    })

    return fuelAddedLogs.reduce((acc, val) => acc + val, 0)
}
